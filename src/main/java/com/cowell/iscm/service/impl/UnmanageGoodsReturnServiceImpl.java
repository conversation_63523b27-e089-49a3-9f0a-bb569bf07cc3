package com.cowell.iscm.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.cowell.iscm.config.Constants;
import com.cowell.iscm.context.GoodsThresholdContext;
import com.cowell.iscm.entity.*;
import com.cowell.iscm.entityTidb.JymlStoreSkuSuggest;
import com.cowell.iscm.entityTidb.JymlStoreSkuSuggestExample;
import com.cowell.iscm.enums.*;
import com.cowell.iscm.enums.scib.ConfigTypeEnum;
import com.cowell.iscm.enums.scib.DicApiEnum;
import com.cowell.iscm.mapper.*;
import com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderDetailExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderExtendMapper;
import com.cowell.iscm.mapper.extend.IscmStoreReturnExecuteOrderMainExtendMapper;
import com.cowell.iscm.mapperTidb.JymlStoreSkuSuggestMapper;
import com.cowell.iscm.rest.errors.BusinessErrorException;
import com.cowell.iscm.service.UnmanageGoodsReturnService;
import com.cowell.iscm.service.dto.TokenUserDTO;
import com.cowell.iscm.service.dto.applyParam.CommonListResponse;
import com.cowell.iscm.service.dto.applyParam.CommonRes;
import com.cowell.iscm.service.dto.returnWarehouse.StoreReturnExecuteOrderQty;
import com.cowell.iscm.service.dto.returnWarehouse.StoreReturnExecuteOrderSum;
import com.cowell.iscm.service.dto.storeAccessAuditor.AmisMap;
import com.cowell.iscm.service.dto.storeAccessAuditor.OptionDto;
import com.cowell.iscm.service.dto.unmanageGoodsReturn.*;
import com.cowell.iscm.service.feign.*;
import com.cowell.iscm.service.feign.dto.*;
import com.cowell.iscm.service.feign.dto.scib.ConfigOrgDetailExtend;
import com.cowell.iscm.service.feign.dto.scib.RuleDetailDTO;
import com.cowell.iscm.service.feign.dto.scib.RuleParam;
import com.cowell.iscm.utils.BeanUtils;
import com.cowell.iscm.utils.DateUtils;
import com.cowell.permission.vo.OrgVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cowell.iscm.config.Constants.ISCM_DEFECTIVE_RETURN_REASON_DEFAULT;
import static com.cowell.iscm.config.Constants.ISCM_DEFECTIVE_RETURN_REASON_NON_EXPIRY;

/**
 * 不经营品退仓服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class UnmanageGoodsReturnServiceImpl implements UnmanageGoodsReturnService {

    private static final Logger logger = LoggerFactory.getLogger(UnmanageGoodsReturnServiceImpl.class);

    @Autowired
    private UnmanageGoodsReturnTaskMapper unmanageGoodsReturnTaskMapper;

    @Autowired
    private IscmStoreReturnExecuteOrderMainExtendMapper iscmStoreReturnExecuteOrderMainExtendMapper;
    @Autowired
    private IscmStoreReturnExecuteOrderMainMapper iscmStoreReturnExecuteOrderMainMapper;
    @Autowired
    private IscmStoreReturnExecuteOrderDetailMapper iscmStoreReturnExecuteOrderDetailMapper;

    @Autowired
    private IscmStoreReturnExecuteOrderDetailExtendMapper iscmStoreReturnExecuteOrderDetailExtendMapper;

    @Autowired
    private JymlStoreSkuSuggestMapper jymlStoreSkuSuggestMapper;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ScibFeignClient scibFeignClient;

    @Autowired
    private ForestService forestService;

    @Autowired
    private SearchApiService searchApiService;

    @Autowired
    private StockcenterService stockcenterService;

    @Autowired
    @Qualifier("returnExecImportExecutor")
    private AsyncTaskExecutor executor;

    // Redis缓存Key
    private final String ISCM_UNMANAGE_GOODS_RETURN_CACHE = "ISCM-UNMANAGE-GOODS-RETURN-CACHE-";
    private final String ISCM_RETURN_WAREHOUSE_ORDER_CODE_CACHE = "ISCM-RETURN-WAREHOUSE-ORDER-CODE-CACHE-";

    public static final String ISCM_RETURN_REASON_NEAR_EXPIRY = "01"; // 近效期
    public static final String ISCM_RETURN_REASON_OVERSTOCK = "02";   // 库存过多
    public static final int BATCH_INSERT_ONCE_MAX_VALUE = 500;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateUnmanageGoodsReturnTaskResponse createTask(TokenUserDTO userDTO, CreateUnmanageGoodsReturnTaskRequest request) throws Exception {
        logger.info("开始创建不经营品退仓任务，用户：{}，参数：{}", userDTO.getUserId(), request);

        // 1. 参数验证
        validateRequest(request);
        // 2. 检查是否有进行中的任务
        if (hasRunningTask(request.getPlatformOrgId())) {
            throw new BusinessErrorException("该平台已有进行中的不经营品退仓任务，请等待完成后再创建新任务");
        }
        // 3. 生成任务编码
        String taskCode = generateTaskCode(request.getPlatformOrgId());
        // 4. 创建任务记录
        UnmanageGoodsReturnTask task = buildTaskEntity(userDTO, request, taskCode);
        int insertResult = unmanageGoodsReturnTaskMapper.insertSelective(task);
        if (insertResult <= 0) {
            throw new BusinessErrorException("创建任务失败");
        }
        // 5. 初始化Redis进度缓存
        initProgressCache(userDTO, taskCode);
        // 6. 异步处理任务
        executor.execute(() -> {
            try {
                processUnmanageGoodsReturnTask(userDTO, task);
            } catch (Exception e) {
                logger.error("异步处理不经营品退仓任务失败，任务编码：{}", taskCode, e);
                updateTaskStatus(task.getId(), UnmanageGoodsReturnTaskStatusEnum.FAILED.getCode(), e.getMessage());
                updateProgressCache(userDTO, taskCode, "处理失败", null, true);
            }
        });
        // 7. 构建响应
        CreateUnmanageGoodsReturnTaskResponse response = new CreateUnmanageGoodsReturnTaskResponse();
        response.setTaskCode(taskCode);
        response.setTaskId(task.getId());
        response.setTaskName(task.getTaskName());
        response.setTaskStatus(task.getTaskStatus());
        response.setTaskStatusDesc(UnmanageGoodsReturnTaskStatusEnum.getNameByCode(task.getTaskStatus()));
        logger.info("不经营品退仓任务创建成功，任务编码：{}，任务ID：{}", taskCode, task.getId());
        return response;
    }

    @Override
    public boolean hasRunningTask(Long platformOrgId) {
        UnmanageGoodsReturnTaskExample example = new UnmanageGoodsReturnTaskExample();
        example.createCriteria()
                .andPlatformOrgIdEqualTo(platformOrgId)
                .andTaskStatusEqualTo(UnmanageGoodsReturnTaskStatusEnum.CALCULATING.getCode())
                .andStatusEqualTo((byte) 0);

        long count = unmanageGoodsReturnTaskMapper.countByExample(example);
        return count > 0;
    }

    @Override
    public UnmanageGoodsReturnProgressDTO getTaskProgress(TokenUserDTO userDTO, String taskCode) {
        String cacheKey = ISCM_UNMANAGE_GOODS_RETURN_CACHE + userDTO.getUserId();
        RBucket<UnmanageGoodsReturnProgressDTO> bucket = redissonClient.getBucket(cacheKey);

        if (bucket.isExists()) {
            UnmanageGoodsReturnProgressDTO progress = bucket.get();
            if (progress != null && taskCode.equals(progress.getTaskCode())) {
                return progress;
            }
        }

        // 如果Redis中没有，从数据库查询任务状态
        UnmanageGoodsReturnTask task = unmanageGoodsReturnTaskMapper.selectByTaskCode(taskCode);
        if (task == null) {
            throw new BusinessErrorException("任务不存在");
        }

        UnmanageGoodsReturnProgressDTO progress = new UnmanageGoodsReturnProgressDTO();
        progress.setTaskCode(taskCode);
        progress.setProcessCount(task.getProcessCount());
        progress.setSuccessCount(task.getSuccessCount());
        progress.setErrorCount(task.getErrorCount());
        progress.setProcessFinished(!task.getTaskStatus().equals(UnmanageGoodsReturnTaskStatusEnum.CALCULATING.getCode()));
        progress.setCurrentStage(UnmanageGoodsReturnTaskStatusEnum.getNameByCode(task.getTaskStatus()));

        return progress;
    }

    /**
     * 参数验证
     */
    private void validateRequest(CreateUnmanageGoodsReturnTaskRequest request) {
        if (request == null) {
            throw new BusinessErrorException("请求参数不能为空");
        }
        if (request.getPlatformOrgId() == null) {
            throw new BusinessErrorException("平台机构ID不能为空");
        }
        if (request.getOrgType() == null || (request.getOrgType() != 1 && request.getOrgType() != 2)) {
            throw new BusinessErrorException("机构类型必须为1（参数配置）或2（自选门店）");
        }
        // 如果是自定义么门店，必须提供门店编码
        if (request.getOrgType() == 2 && StringUtils.isEmpty(request.getOrgIds())) {
            throw new BusinessErrorException("自定义商品类型时，门店编码集合不能为空");
        }

        if (request.getGoodsType() == null || (request.getGoodsType() != 1 && request.getGoodsType() != 2)) {
            throw new BusinessErrorException("商品类型必须为1（全部不经营商品）或2（自定义商品）");
        }
        // 如果是自定义商品，必须提供商品编码
        if (request.getGoodsType() == 2 && StringUtils.isEmpty(request.getGoodsNos())) {
            throw new BusinessErrorException("自定义商品类型时，商品编码集合不能为空");
        }
        if (request.getGoodsType() == 2 && request.getGoodsNos().split(",").length > 1000) {
            throw new BusinessErrorException("自定义商品类型时，商品编码最大支持1000个");
        }
    }

    /**
     * 生成任务编码
     * 格式：UGR + yyyyMMdd + 平台ID后4位 + 4位序号
     */
    private String generateTaskCode(Long platformOrgId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String platformSuffix = String.format("%04d", platformOrgId % 10000);
        // 查询当天该平台的任务数量
        UnmanageGoodsReturnTaskExample example = new UnmanageGoodsReturnTaskExample();
        example.createCriteria()
                .andPlatformOrgIdEqualTo(platformOrgId)
                .andTaskCodeLike("UGR" + dateStr + "%");
        long count = unmanageGoodsReturnTaskMapper.countByExample(example);
        String sequence = String.format("%04d", count + 1);
        return "UGR" + dateStr + platformSuffix + sequence;
    }

    /**
     * 构建任务实体
     */
    private UnmanageGoodsReturnTask buildTaskEntity(TokenUserDTO userDTO, CreateUnmanageGoodsReturnTaskRequest request, String taskCode) throws Exception {
        UnmanageGoodsReturnTask task = new UnmanageGoodsReturnTask();
        // 基本信息
        task.setTaskCode(taskCode);
        task.setPlatformOrgId(request.getPlatformOrgId());
        OrgVO orgInfoById = permissionService.getOrgInfoById(request.getPlatformOrgId());
        task.setPlatformName(orgInfoById.getShortName());
        task.setTaskName(orgInfoById.getShortName()+"不经营品退仓"+taskCode);
        // 机构信息
        task.setOrgType(request.getOrgType());
        task.setOrgIds(request.getOrgIds());
        // 商品信息
        task.setGoodsType(request.getGoodsType());
        task.setGoodsNos(request.getGoodsNos());
        // 状态信息
        task.setTaskStatus(UnmanageGoodsReturnTaskStatusEnum.CALCULATING.getCode());
        task.setStoreCount(0);
        task.setGoodsCount(0);
        task.setProcessCount(0);
        task.setSuccessCount(0);
        task.setErrorCount(0);
        // 系统字段
        task.setStatus((byte) 0);
        task.setVersion(1);
        task.setStartTime(new Date());
        task.setCreatedBy(userDTO.getUserId());
        task.setCreatedName(userDTO.getUserName());
        
        return task;
    }

    /**
     * 初始化Redis进度缓存
     */
    private void initProgressCache(TokenUserDTO userDTO, String taskCode) {
        String cacheKey = ISCM_UNMANAGE_GOODS_RETURN_CACHE + userDTO.getUserId();
        RBucket<UnmanageGoodsReturnProgressDTO> bucket = redissonClient.getBucket(cacheKey);
        UnmanageGoodsReturnProgressDTO progress = new UnmanageGoodsReturnProgressDTO();
        progress.setTaskCode(taskCode);
        progress.setProcessCount(0);
        progress.setSuccessCount(0);
        progress.setErrorCount(0);
        progress.setProcessFinished(false);
        progress.setCurrentStage("初始化中");
        progress.setStartTime(System.currentTimeMillis());
        progress.setErrorList(new ArrayList<>());

        bucket.set(progress, 12L, TimeUnit.HOURS);
        logger.info("初始化进度缓存成功，任务编码：{}", taskCode);
    }

    /**
     * 更新Redis进度缓存
     */
    private void updateProgressCache(TokenUserDTO userDTO, String taskCode, String currentStage, String currentStore, boolean finished) {
        String cacheKey = ISCM_UNMANAGE_GOODS_RETURN_CACHE + userDTO.getUserId();
        RBucket<UnmanageGoodsReturnProgressDTO> bucket = redissonClient.getBucket(cacheKey);
        if (bucket.isExists()) {
            UnmanageGoodsReturnProgressDTO progress = bucket.get();
            if (progress != null && taskCode.equals(progress.getTaskCode())) {
                progress.setCurrentStage(currentStage);
                progress.setCurrentStore(currentStore);
                progress.setProcessFinished(finished);
                if (finished) {
                    progress.setEndTime(System.currentTimeMillis());
                }
                bucket.set(progress, 12L, TimeUnit.HOURS);
            }
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, Byte status, String errorMsg) {
        UnmanageGoodsReturnTask updateTask = new UnmanageGoodsReturnTask();
        updateTask.setId(taskId);
        updateTask.setTaskStatus(status);
        updateTask.setErrorMsg(errorMsg);
        if (UnmanageGoodsReturnTaskStatusEnum.COMPLETED.getCode().equals(status) ||
            UnmanageGoodsReturnTaskStatusEnum.FAILED.getCode().equals(status)) {
            updateTask.setEndTime(new Date());
        }
        unmanageGoodsReturnTaskMapper.updateByPrimaryKeySelective(updateTask);
    }

    /**
     * 更新任务机构或商品
     */
    private void updateTaskOrgOrGoods(Long taskId, List<OrgTreeSimpleDTO> targetStores , String goodsNos) {
        UnmanageGoodsReturnTask updateTask = new UnmanageGoodsReturnTask();
        updateTask.setId(taskId);
        if (!CollectionUtils.isEmpty(targetStores)) {
            String orgIds = targetStores.stream()
                    .map(OrgTreeSimpleDTO::getId)  // 提取ID
                    .distinct()
                    .map(String::valueOf)        // 去重
                    .collect(Collectors.joining(","));
            updateTask.setOrgIds(orgIds);
            updateTask.setStoreCount(orgIds.length());
            updateTask.setProcessCount(orgIds.length());
            unmanageGoodsReturnTaskMapper.updateByPrimaryKeySelective(updateTask);
        }
        if (StringUtils.hasText(goodsNos)) {
            updateTask.setGoodsNos(goodsNos);
            unmanageGoodsReturnTaskMapper.updateByPrimaryKeySelective(updateTask);
        }
    }

    /**
     * 异步处理不经营品退仓任务
     */
    private void processUnmanageGoodsReturnTask(TokenUserDTO userDTO, UnmanageGoodsReturnTask task) throws Exception {
        logger.info("开始异步处理不经营品退仓任务，任务编码：{}", task.getTaskCode());

        try {
            // 初始化商品阈值上下文
            GoodsThresholdContext.init();
            // 1. 门店筛选和权限验证
            updateProgressCache(userDTO, task.getTaskCode(), "门店筛选中", null, false);
            List<OrgTreeSimpleDTO> targetStores = filterAndValidateStores(userDTO, task);
            // 1.1 更新实际生效的门店org
            updateTaskOrgOrGoods(task.getId(),targetStores, null);

            // 2. 商品筛选和可退验证
            updateProgressCache(userDTO, task.getTaskCode(), "商品筛选中", null, false);
            Map<Long, List<String>> storeGoodsMap = filterAndValidateGoodsByStore(task, targetStores);

            // 3. 更新任务统计信息
            int totalGoodsCount = storeGoodsMap.values().stream().mapToInt(List::size).sum();
            updateTaskCounts(task.getId(), targetStores.size(), totalGoodsCount);

            // 4. 按门店处理
            updateProgressCache(userDTO, task.getTaskCode(), "门店处理中", null, false);
            processStoresByBatch(userDTO, task, storeGoodsMap);

            // 5. 完成处理
            updateTaskStatus(task.getId(), UnmanageGoodsReturnTaskStatusEnum.COMPLETED.getCode(), null);
            updateProgressCache(userDTO, task.getTaskCode(), "处理完成", null, true);

            logger.info("不经营品退仓任务处理完成，任务编码：{}", task.getTaskCode());

        } catch (Exception e) {
            logger.error("处理不经营品退仓任务失败，任务编码：{}", task.getTaskCode(), e);
            updateTaskStatus(task.getId(), UnmanageGoodsReturnTaskStatusEnum.FAILED.getCode(), e.getMessage());
            updateProgressCache(userDTO, task.getTaskCode(), "处理失败：" + e.getMessage(), null, true);
            throw e;
        } finally {
            // 清理商品阈值上下文，避免内存泄漏
            GoodsThresholdContext.clear();
        }
    }

    /**
     * 门店筛选和权限验证
     */
    private List<OrgTreeSimpleDTO> filterAndValidateStores(TokenUserDTO userDTO, UnmanageGoodsReturnTask task) throws Exception {
        logger.info("开始门店筛选和权限验证，任务编码：{}", task.getTaskCode());

        try {
            // 1. 获取用户有权限的门店
            List<OrgTreeSimpleDTO> userStores = getUserAuthorizedStores(userDTO, task.getPlatformOrgId());
            // 2. 根据机构类型筛选目标门店
            List<OrgTreeSimpleDTO> targetStores = filterTargetStoresByOrgType(task, userStores);
            if (targetStores.isEmpty()) {
                throw new BusinessErrorException("没有有效的目标门店，请检查门店配置和权限");
            }
            logger.info("门店筛选完成，目标门店数量：{}，任务编码：{}", targetStores.size(), task.getTaskCode());
            return targetStores;
        } catch (BusinessErrorException e) {
            throw e;
        } catch (Exception e) {
            logger.error("门店筛选和权限验证失败，任务编码：{}", task.getTaskCode(), e);
            throw new BusinessErrorException("门店筛选失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户有权限的门店
     */
    private  List<OrgTreeSimpleDTO> getUserAuthorizedStores(TokenUserDTO userDTO, Long platformOrgId) throws Exception {
        List<OrgTreeSimpleDTO> userStores = permissionService.listUserDataScopeTreesByOrgIdAndTypes(
                userDTO.getUserId(), platformOrgId, Lists.newArrayList(OrgTypeEnum.ORG_TYPE_STORE.getCode()));
        if (CollectionUtils.isEmpty(userStores) || CollectionUtils.isEmpty(userStores.get(0).getChildren())) {
            throw new BusinessErrorException("您没有门店的操作权限");
        }
        return userStores.get(0).getChildren();
    }

    /**
     * 根据机构类型筛选目标门店
     */
    private List<OrgTreeSimpleDTO> filterTargetStoresByOrgType(UnmanageGoodsReturnTask task,  List<OrgTreeSimpleDTO> userStores) throws Exception {
        Map<Long, OrgTreeSimpleDTO> orgIdMap = userStores.stream().collect(Collectors.toMap(OrgTreeSimpleDTO::getId, Function.identity(), (k1, k2) -> k1));
        Map<Long, OrgTreeSimpleDTO> storeIdMap = userStores.stream().collect(Collectors.toMap(OrgTreeSimpleDTO::getOutId, Function.identity(), (k1, k2) -> k1));
        if (task.getOrgType() == 1) {
            // 参数配置模式
            return filterStoresByParameterConfig(task, storeIdMap);
        } else {
            // 自选门店模式
            return filterStoresByManualSelection(task, orgIdMap);
        }
    }

    /**
     * 自选门店模式筛选
     */
    private List<OrgTreeSimpleDTO> filterStoresByManualSelection(UnmanageGoodsReturnTask task, Map<Long, OrgTreeSimpleDTO> orgIdMap) {
        List<OrgTreeSimpleDTO> targetStores = new ArrayList<>();
        if (!StringUtils.hasText(task.getOrgIds())) {
            logger.warn("自选门店模式下机构ID为空，任务编码：{}", task.getTaskCode());
            return targetStores;
        }
        String[] orgIdArray = task.getOrgIds().split(",");
        for (String orgIdStr : orgIdArray) {
            if (!StringUtils.hasText(orgIdStr)) {
                continue;
            }
            try {
                Long orgId = Long.valueOf(orgIdStr.trim());
                if (orgIdMap.containsKey(orgId)) {
                    targetStores.add(orgIdMap.get(orgId));
                } else {
                    logger.warn("用户无权限访问门店：{}，任务编码：{}", orgId, task.getTaskCode());
                }
            } catch (NumberFormatException e) {
                logger.warn("无效的门店ID格式：{}，任务编码：{}", orgIdStr, task.getTaskCode());
            }
        }
        return targetStores;
    }

    /**
     * 参数配置模式筛选
     */
    private List<OrgTreeSimpleDTO> filterStoresByParameterConfig(UnmanageGoodsReturnTask task, Map<Long, OrgTreeSimpleDTO> storeIdMap) throws Exception {
        try {
            // 1. 获取门店选择器配置的门店列表
            List<Long> configuredStoreIds = getConfiguredStoreIds(task.getPlatformOrgId());

            // 2. 获取业务过滤规则
            BusinessFilterRules filterRules = getBusinessFilterRules(task.getPlatformOrgId());

            // 3. 筛选符合条件的门店
            return filterStoresByBusinessRules(configuredStoreIds, filterRules.getExcludeOrgIds(), storeIdMap, task.getTaskCode());

        } catch (Exception e) {
            logger.error("参数配置模式门店筛选失败，任务编码：{}", task.getTaskCode(), e);
            throw new BusinessErrorException("参数配置门店筛选失败：" + e.getMessage());
        }
    }
    /**
     * 获取配置的门店ID列表
     */
    private List<Long> getConfiguredStoreIds(Long platformOrgId) throws Exception {
        ResponseEntity<CommonRes<ScibCommonResultDTO>> responseEntity = scibFeignClient.getStoreIdListByStoreSelectorAndConfig(
                platformOrgId, DicApiEnum.BLKC_4_3.getCode(), ConfigTypeEnum.BLCK.getType().toString());

        if (!isValidResponse(responseEntity)) {
            logger.error("调用Scib获取门店选择器配置失败，平台ID：{}", platformOrgId);
            throw new BusinessErrorException("获取门店配置失败");
        }

        CommonRes<ScibCommonResultDTO> commonRes = responseEntity.getBody();
        if (commonRes == null || !Constants.INT_ZERO.equals(commonRes.getStatus())) {
            throw new BusinessErrorException("门店选择器配置调用异常");
        }

        ScibCommonResultDTO data = commonRes.getData();
        if (data == null || CollectionUtils.isEmpty(data.getStoreIdList())) {
            throw new BusinessErrorException("未找到配置的门店列表");
        }

        return data.getStoreIdList();
    }

    /**
     * 获取业务过滤规则
     */
    private BusinessFilterRules getBusinessFilterRules(Long platformOrgId) {
        BusinessFilterRules rules = new BusinessFilterRules();
        try {
            RuleParam ruleParam = new RuleParam();
            ruleParam.setConfigType(ConfigTypeEnum.BLCK.getType().toString());
            ruleParam.setOrgId(platformOrgId);
            ruleParam.setScopeCode(DicApiEnum.BLKC_4_3.getCode());
            logger.info("ruleParam={}",ruleParam);
            ResponseEntity<CommonRes<AmisMap>> ruleResponse = scibFeignClient.getRuleList(ruleParam);
            logger.info("ruleResponse={}",ruleResponse);
            if (!isValidResponse(ruleResponse)) {
                logger.warn("获取业务过滤规则失败，平台ID：{}，使用空过滤规则", platformOrgId);
                return rules;
            }
            CommonRes<AmisMap> commonRes = ruleResponse.getBody();
            if (commonRes != null && Constants.INT_ZERO.equals(commonRes.getStatus())) {
                AmisMap amisMap = commonRes.getData();
                if (amisMap != null && amisMap.containsKey("resultMap")) {
                    // 获取 resultMap 中的数据
                    Object resultMapObj = amisMap.get("resultMap");
                    if (resultMapObj instanceof HashMap) {
                        HashMap resultMap = (HashMap) resultMapObj;
                        // 获取需要排除的连锁机构ID列表
                        extractExcludeOrgIds(resultMap, rules, platformOrgId);
                        // 获取需要排除的商品编码列表
                        extractExcludeGoodsNos(resultMap, rules, platformOrgId);
                    } else {
                        logger.warn("resultMap 数据类型不正确，平台ID：{}，类型：{}", platformOrgId,
                                resultMapObj != null ? resultMapObj.getClass().getSimpleName() : "null");
                    }
                } else {
                    logger.debug("响应数据中没有 resultMap 字段，平台ID：{}", platformOrgId);
                }
            } else {
                logger.warn("业务规则接口调用失败，平台ID：{}，状态：{}", platformOrgId, commonRes != null ? commonRes.getStatus() : "null");
            }
        } catch (Exception e) {
            logger.warn("获取业务过滤规则异常，平台ID：{}，使用空过滤规则", platformOrgId, e);
        }

        return rules;
    }

    /**
     * 业务过滤规则内部类
     */
    private static class BusinessFilterRules {
        private List<Long> excludeOrgIds = new ArrayList<>();
        private List<String> excludeGoodsNos = new ArrayList<>();

        public List<Long> getExcludeOrgIds() {
            return excludeOrgIds;
        }

        public void setExcludeOrgIds(List<Long> excludeOrgIds) {
            this.excludeOrgIds = excludeOrgIds != null ? excludeOrgIds : new ArrayList<>();
        }

        public List<String> getExcludeGoodsNos() {
            return excludeGoodsNos;
        }

        public void setExcludeGoodsNos(List<String> excludeGoodsNos) {
            this.excludeGoodsNos = excludeGoodsNos != null ? excludeGoodsNos : new ArrayList<>();
        }
    }

    /**
     * 根据业务规则筛选门店
     */
    private List<OrgTreeSimpleDTO> filterStoresByBusinessRules(List<Long> configuredStoreIds, List<Long> filterBusinessOrgIds,
                                                              Map<Long, OrgTreeSimpleDTO> storeIdMap, String taskCode) {
        List<OrgTreeSimpleDTO> targetStores = new ArrayList<>();

        for (Long storeId : configuredStoreIds) {
            if (!storeIdMap.containsKey(storeId)) {
                logger.warn("用户无权限访问门店：{}，任务编码：{}", storeId, taskCode);
                continue;
            }

            OrgTreeSimpleDTO store = storeIdMap.get(storeId);

            // 检查是否被业务规则过滤
            if (isStoreFilteredByBusinessRules(store, filterBusinessOrgIds)) {
                logger.debug("门店被业务规则过滤：{}，任务编码：{}", storeId, taskCode);
                continue;
            }

            targetStores.add(store);
        }

        return targetStores;
    }

    /**
     * 检查门店是否被业务规则过滤
     */
    private boolean isStoreFilteredByBusinessRules(OrgTreeSimpleDTO store, List<Long> filterBusinessOrgIds) {
        if (CollectionUtils.isEmpty(filterBusinessOrgIds) || !StringUtils.hasText(store.getOrgPath())) {
            return false;
        }

        String[] pathIds = store.getOrgPath().split("/");
        for (String pathId : pathIds) {
            if (!StringUtils.hasText(pathId)) {
                continue;
            }

            try {
                Long orgId = Long.parseLong(pathId.trim());
                if (filterBusinessOrgIds.contains(orgId)) {
                    return true; // 被过滤
                }
            } catch (NumberFormatException e) {
                logger.debug("无效的机构路径ID格式：{}", pathId);
            }
        }

        return false; // 不被过滤
    }


    /**
     * 商品筛选和可退验证（按门店分组）
     */
    private Map<Long, List<String>> filterAndValidateGoodsByStore(UnmanageGoodsReturnTask task, List<OrgTreeSimpleDTO> targetStores) throws Exception {
        logger.info("开始商品筛选和可退验证，任务编码：{}", task.getTaskCode());
        try {
            // 获取业务过滤规则（包含商品黑名单）
            BusinessFilterRules filterRules = getBusinessFilterRules(task.getPlatformOrgId());
            Map<Long, List<String>> storeGoodsMap = new HashMap<>();
            if (task.getGoodsType() == 1) {
                // 全部不经营商品：按门店查询经营目录
                storeGoodsMap = getAllUnmanageGoodsByStore(targetStores, filterRules.getExcludeGoodsNos(), task.getTaskCode());
            } else {
                // 自定义商品：为每个门店分配相同的商品列表
                List<String> customGoods = getCustomGoodsWithFilter(task.getGoodsNos(),new ArrayList<>(), task.getTaskCode());
                for (OrgTreeSimpleDTO store : targetStores) {
                    storeGoodsMap.put(store.getOutId(), new ArrayList<>(customGoods));
                }
            }
            // 验证商品可退条件
            Map<Long, List<String>> validatedStoreGoodsMap = validateGoodsReturnability(storeGoodsMap, task);
            int totalGoods = validatedStoreGoodsMap.values().stream().mapToInt(List::size).sum();
            logger.info("商品筛选完成，涉及门店：{}个，总商品数量：{}，任务编码：{}", validatedStoreGoodsMap.size(), totalGoods, task.getTaskCode());
            return validatedStoreGoodsMap;

        } catch (Exception e) {
            logger.error("商品筛选和可退验证失败，任务编码：{}", task.getTaskCode(), e);
            throw new BusinessErrorException("商品筛选失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有门店的不经营商品（按门店分组，排除黑名单）
     */
    private Map<Long, List<String>> getAllUnmanageGoodsByStore(List<OrgTreeSimpleDTO> targetStores, List<String> excludeGoodsNos, String taskCode) throws Exception {
        Map<Long, List<String>> storeGoodsMap = new HashMap<>();

        for (OrgTreeSimpleDTO store : targetStores) {
            try {
                List<String> storeUnmanageGoods = getStoreUnmanageGoods(store.getId(), excludeGoodsNos);
                storeGoodsMap.put(store.getOutId(), storeUnmanageGoods);
                logger.debug("门店{}不经营商品数量：{}，任务编码：{}", store.getShortName(), storeUnmanageGoods.size(), taskCode);
            } catch (Exception e) {
                logger.warn("获取门店{}不经营商品失败，任务编码：{}，错误：{}", store.getShortName(), taskCode, e.getMessage());
                // 单个门店失败时，为该门店设置空列表，不影响其他门店处理
                storeGoodsMap.put(store.getOutId(), new ArrayList<>());
            }
        }
        return storeGoodsMap;
    }

    /**
     * 获取单个门店的不经营商品
     */
    private List<String> getStoreUnmanageGoods(Long storeId, List<String> excludeGoodsNos) throws Exception {
        // 调用门店商品经营目录服务
        ResponseEntity<CommonRes<ScibCommonResultDTO>> response = scibFeignClient.getStoreGoodsContents(storeId, 4, null); // manageStatus = 4 (NON_MANAGE 不经营)

        if (!isValidResponse(response)) {
            logger.warn("获取门店{}经营目录失败", storeId);
            return new ArrayList<>();
        }

        CommonRes<ScibCommonResultDTO> commonRes = response.getBody();
        if (commonRes == null || !Constants.INT_ZERO.equals(commonRes.getStatus())) {
            logger.warn("门店{}经营目录调用异常", storeId);
            return new ArrayList<>();
        }

        ScibCommonResultDTO data = commonRes.getData();
        if (data == null || CollectionUtils.isEmpty(data.getStoreGoodsContents())) {
            logger.debug("门店{}没有不经营商品", storeId);
            return new ArrayList<>();
        }

        // 获取门店商品目录列表
        List<String> storeGoodsList = data.getStoreGoodsContents();

        // 过滤掉黑名单中的商品
        List<String> filteredGoods = storeGoodsList.stream()
                .filter(goodsNo -> !excludeGoodsNos.contains(goodsNo))
                .collect(Collectors.toList());

        logger.debug("门店{}不经营商品{}个，过滤后{}个", storeId, storeGoodsList.size(), filteredGoods.size());

        return filteredGoods;
    }

    /**
     * 获取自定义商品（排除黑名单）
     */
    private List<String> getCustomGoodsWithFilter(String goodsNosStr, List<String> excludeGoodsNos, String taskCode) {
        if (!StringUtils.hasText(goodsNosStr)) {
            logger.warn("自定义商品模式下商品编码为空，任务编码：{}", taskCode);
            return new ArrayList<>();
        }
        String[] goodsArray = goodsNosStr.split(",");
        List<String> filteredGoods = new ArrayList<>();
        for (String goodsNo : goodsArray) {
            if (!StringUtils.hasText(goodsNo)) {
                continue;
            }
            String trimmedGoodsNo = goodsNo.trim();
            filteredGoods.add(trimmedGoodsNo);
        }
        logger.info("自定义商品{}个，过滤后{}个，任务编码：{}", goodsArray.length, filteredGoods.size(), taskCode);
        return filteredGoods;
    }

    /**
     * 更新任务统计信息
     */
    private void updateTaskCounts(Long taskId, int storeCount, int goodsCount) {
        UnmanageGoodsReturnTask updateTask = new UnmanageGoodsReturnTask();
        updateTask.setId(taskId);
        updateTask.setStoreCount(storeCount);
        updateTask.setGoodsCount(goodsCount);
        updateTask.setProcessCount(storeCount); // 处理数量等于门店数量
        unmanageGoodsReturnTaskMapper.updateByPrimaryKeySelective(updateTask);
    }

    /**
     * 按门店批量处理
     */
    private void processStoresByBatch(TokenUserDTO userDTO, UnmanageGoodsReturnTask task,
                                     Map<Long, List<String>> storeGoodsMap) {
        logger.info("开始按门店批量处理，门店数量：{}，任务编码：{}", storeGoodsMap.size(), task.getTaskCode());

        for (Map.Entry<Long, List<String>> entry : storeGoodsMap.entrySet()) {
            Long storeId = entry.getKey();
            List<String> storeGoods = entry.getValue();
            try {
                // 获取门店信息（用于日志显示）
                updateProgressCache(userDTO, task.getTaskCode(), "处理门店", storeId.toString(), false);
                // 处理单个门店的商品
                processStoreGoods(userDTO, task, storeId, storeGoods);
                logger.info("门店处理完成：{}（ID:{}），商品数量：{}，任务编码：{}",    storeId.toString(), storeId, storeGoods.size(), task.getTaskCode());
                // 更新成功计数
                updateSuccessCount(task.getId());
            } catch (Exception e) {
                logger.error("门店处理失败：ID={}，任务编码：{}", storeId, task.getTaskCode(), e);
                // 更新错误计数
                updateErrorCount(task.getId());
                // TODO: 记录错误信息到错误日志表
            }
        }
    }

    /**
     * 处理单个门店的商品
     */
    private void processStoreGoods(TokenUserDTO userDTO, UnmanageGoodsReturnTask task, Long storeId, List<String> goodsNos) throws Exception {
        if (CollectionUtils.isEmpty(goodsNos)) {
            logger.debug("门店{}没有需要处理的商品，任务编码：{}", storeId, task.getTaskCode());
            return;
        }
        logger.debug("开始处理门店{}的{}个商品，任务编码：{}", storeId, goodsNos.size(), task.getTaskCode());
        // 1. 查询门店库存
        List<OrgInfoBaseCache> orgInfoBaseCacheListByStoreIdsCache = permissionService.getOrgInfoBaseCacheListByStoreIdsCache(Lists.newArrayList(storeId));
        if (CollectionUtils.isEmpty(orgInfoBaseCacheListByStoreIdsCache)) {
            logger.error("门店:{}不存在", storeId);
            return;
        }
        OrgInfoBaseCache store = orgInfoBaseCacheListByStoreIdsCache.get(0);
        DropBoxParam param = new DropBoxParam();
        param.setType(Constants.ISCM_RETURN_REASON_TYPE);// 写死
        param.setPropertyCode(Constants.ISCM_RETURN_REASON_FLAG);
        CommonListResponse<OptionDto> commonRes = Optional.ofNullable(scibFeignClient.dropBox(param).getBody()).orElseThrow(() -> new BusinessErrorException("查询退仓原因失败,请联系管理员"));
        Map<String, String> reasonMap = Optional.ofNullable(commonRes.getData()).orElseThrow(() -> new BusinessErrorException("查询退仓原因失败,请联系管理员")).stream().collect(Collectors.toMap(OptionDto::getValue, OptionDto::getLabel, (k1,k2) -> k1));
        logger.info("reasonMap:{}", reasonMap);
        Map<String, SpuListVo> goodsMap = new HashMap<>();
        Map<String, List<StockGoodsBatchCodeSimpleInfo>> stockMap = new HashMap<>();
        Map<String, AuctionSpuBaseInfo> auctionSpuBaseMap = new HashMap<>();
        try {
            SpuQueryParamVo spuParam = new SpuQueryParamVo();
            spuParam.setGoodsNoList(goodsNos);
            goodsMap.putAll(searchApiService.getSupContentsMap(spuParam));
        } catch (Exception e) {
            logger.error("门店:{}查询商品失败", storeId);
            throw new BusinessErrorException("门店:" + storeId + "查询商品失败");
        }
        try{
            StockGoodsPagableQueryParam stockParam = new StockGoodsPagableQueryParam();
            stockParam.setGoodsNos(goodsNos);
            stockParam.setBusinessId(store.getBusinessId());
            stockParam.setStockGreaterThan(BigDecimal.ZERO);
            stockParam.setStoreId(store.getOutId());
            stockMap.putAll(stockcenterService.goodsBatchCodePage(stockParam));
        } catch (Exception e) {
            logger.error("门店:{}查询商品库存失败", storeId);
            throw new BusinessErrorException("门店:" + storeId + "查询商品库存失败");
        }
        try{
            auctionSpuBaseMap.putAll(forestService.batchFindSpuProperty(goodsNos, store.getBusinessId()).stream().collect(Collectors.toMap(AuctionSpuBaseInfo::getGoodsNo, Function.identity(), (k1,k2) -> k1)));
        } catch (Exception e) {
            logger.error("门店:{}查询商品属性失败", storeId);
            throw new BusinessErrorException("门店:" + storeId + "查询商品属性失败");
        }
        JymlStoreSkuSuggestExample jymlStoreSkuSuggestExample=new JymlStoreSkuSuggestExample();
        jymlStoreSkuSuggestExample.createCriteria().andBusinessOrgIdEqualTo(store.getBusinessOrgId()).andStoreIdEqualTo(storeId).andGoodsNoIn(goodsNos);
        List<JymlStoreSkuSuggest> jymlStoreSkuSuggests = jymlStoreSkuSuggestMapper.selectByExample(jymlStoreSkuSuggestExample);
        Map<String, JymlStoreSkuSuggest> suggestMap = jymlStoreSkuSuggests.stream().collect(Collectors.toMap(JymlStoreSkuSuggest::getGoodsNo, Function.identity(), (k1, k2) -> k1));
        Map<String, ReturnCalculationResult> storeGoodsReturnResultsMap=new HashMap<>();
        for (String goodsNo : goodsNos) {
            SpuListVo spuListVo = goodsMap.get(goodsNo);
            if (null == spuListVo) {
                logger.info("门店:{}商品:{}不存在", storeId, goodsNo);
                continue;
            }
            List<StockGoodsBatchCodeSimpleInfo> batchStocks = stockMap.get(goodsNo);
            if (CollectionUtils.isEmpty(batchStocks)) {
               logger.info("门店:{}商品:{}没有库存", storeId, goodsNo);
                continue;
            }
            Map<String, List<StockGoodsBatchCodeSimpleInfo>> batchMap = batchStocks.stream().collect(Collectors.groupingBy(StockGoodsBatchCodeSimpleInfo::getBatchNo));
            // 计算退仓数量
            ReturnCalculationResult returnResult = calculateReturnQuantity(store, goodsNo, batchMap, task.getTaskCode(),suggestMap,userDTO);
            if (returnResult.getTotalReturnQuantity().compareTo(BigDecimal.ZERO) > 0) {
                logger.info("门店:{}商品:{}需要退仓，近效期数量:{}，非近效期数量:{}，总退仓数量:{}",storeId, goodsNo,
                        returnResult.getNearExpiryQuantity(), returnResult.getNonExpiryReturnQuantity(), returnResult.getTotalReturnQuantity());
                storeGoodsReturnResultsMap.put(goodsNo,returnResult);
            } else {
                logger.info("门店:{}商品:{}无需退仓", storeId, goodsNo);
            }
            logger.debug("门店{}商品处理完成，任务编码：{}", storeId, task.getTaskCode());
        }
        if(storeGoodsReturnResultsMap.isEmpty()){
            logger.debug("门店{}商品处理完成，无需退仓，任务编码：{}", storeId, task.getTaskCode());
            return;
        }
        generateStoreReturnExecuteOrder(store,storeGoodsReturnResultsMap,goodsMap,stockMap,auctionSpuBaseMap,reasonMap,userDTO);
    }
    /**
     * 更新成功计数
     */
    private void updateSuccessCount(Long taskId) {
        UnmanageGoodsReturnTask task = unmanageGoodsReturnTaskMapper.selectByPrimaryKey(taskId);
        if (task != null) {
            UnmanageGoodsReturnTask updateTask = new UnmanageGoodsReturnTask();
            updateTask.setId(taskId);
            updateTask.setSuccessCount((task.getSuccessCount() == null ? 0 : task.getSuccessCount()) + 1);
            unmanageGoodsReturnTaskMapper.updateByPrimaryKeySelective(updateTask);
        }
    }

    /**
     * 更新错误计数
     */
    private void updateErrorCount(Long taskId) {
        UnmanageGoodsReturnTask task = unmanageGoodsReturnTaskMapper.selectByPrimaryKey(taskId);
        if (task != null) {
            UnmanageGoodsReturnTask updateTask = new UnmanageGoodsReturnTask();
            updateTask.setId(taskId);
            updateTask.setErrorCount((task.getErrorCount() == null ? 0 : task.getErrorCount()) + 1);
            unmanageGoodsReturnTaskMapper.updateByPrimaryKeySelective(updateTask);
        }
    }

    /**
     * 提取需要排除的连锁机构ID列表
     */
    private void extractExcludeOrgIds(HashMap resultMap, BusinessFilterRules rules, Long platformOrgId) {
        try {
            if (resultMap.containsKey("blkc_4_3_businesslist")) {
                Object businessListObj = resultMap.get("blkc_4_3_businesslist");
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    RuleDetailDTO businessList = objectMapper.convertValue(businessListObj, RuleDetailDTO.class);
                    if (businessList != null && !CollectionUtils.isEmpty(businessList.getOrgIdList())) {
                        rules.setExcludeOrgIds(businessList.getOrgIdList());
                        logger.debug("获取到排除连锁机构{}个，平台ID：{}", businessList.getOrgIdList().size(), platformOrgId);
                    }
                } catch (Exception e) {
                    logger.error("转换 businessList 对象失败，平台ID：{}", platformOrgId, e);
                }
            }
        } catch (Exception e) {
            logger.warn("提取排除连锁机构ID失败，平台ID：{}", platformOrgId, e);
        }
    }

    /**
     * 提取需要排除的商品编码列表
     */
    private void extractExcludeGoodsNos(HashMap resultMap, BusinessFilterRules rules, Long platformOrgId) {
        try {
            if (resultMap.containsKey("blkc_4_3_goodsBlackList")) {
                Object goodsBlackListObj = resultMap.get("blkc_4_3_goodsBlackList");
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    RuleDetailDTO goodsBlackList = objectMapper.convertValue(goodsBlackListObj, RuleDetailDTO.class);
                    if (goodsBlackList != null && !CollectionUtils.isEmpty(goodsBlackList.getGoodsCodeList())) {
                        // 从 goodsCodeList 中提取商品编码
                        List<String> goodsNos = goodsBlackList.getGoodsCodeList().stream()
                                .map(ConfigOrgDetailExtend::getKeyword)
                                .filter(StringUtils::hasText)
                                .collect(Collectors.toList());
                        rules.setExcludeGoodsNos(goodsNos);
                        logger.debug("获取到排除商品{}个，平台ID：{}", goodsNos.size(), platformOrgId);
                    }
                } catch (Exception e) {
                    logger.error("转换 businessList 对象失败，平台ID：{}", platformOrgId, e);
                }
            }
        } catch (Exception e) {
            logger.warn("提取排除商品编码失败，平台ID：{}", platformOrgId, e);
        }
    }

    /**
     * 验证商品可退性
     * 验证条件：退仓说明=可退仓 AND 返厂说明=无条件可退
     */
    private Map<Long, List<String>> validateGoodsReturnability(Map<Long, List<String>> storeGoodsMap, UnmanageGoodsReturnTask task) throws Exception {
        logger.info("开始验证商品可退性，任务编码：{}", task.getTaskCode());

        // 收集所有需要验证的商品编码
        List<OrgInfoBaseCache> orgInfoBaseCacheListByStoreIdsCache = permissionService.getOrgInfoBaseCacheListByStoreIdsCache(new ArrayList<>(storeGoodsMap.keySet()));
        Map<Long, OrgInfoBaseCache> baseStoreCacheMap = orgInfoBaseCacheListByStoreIdsCache.stream().collect(Collectors.toMap(OrgInfoBaseCache::getOutId, Function.identity(), (k1, k2) -> k1));
        // 获取效期标准配置
        Map<String, Integer> validPeriodThresholds = getValidPeriodThresholds(task.getPlatformOrgId());
        try {
            // 验证每个门店的商品
            Map<Long, List<String>> validatedStoreGoodsMap = new HashMap<>();
            int totalValidatedGoods = 0;
            int totalFilteredGoods = 0;
            for (Map.Entry<Long, List<String>> entry : storeGoodsMap.entrySet()) {
                Long storeId = entry.getKey();
                if (baseStoreCacheMap.containsKey(storeId)){
                    OrgInfoBaseCache orgInfoBaseCache = baseStoreCacheMap.get(storeId);
                    if(orgInfoBaseCache == null){
                        continue;
                    }
                    List<String> storeGoods = entry.getValue();
                    // 批量查询商品属性
                    Map<String, AuctionSpuBaseInfo> auctionSpuMap = forestService.batchFindSpuPropertyByComId(storeGoods, orgInfoBaseCache.getBusinessId(),orgInfoBaseCache.getComId())
                            .stream().collect(Collectors.toMap(AuctionSpuBaseInfo::getGoodsNo, Function.identity(), (k1, k2) -> k1));
                    // 为商品设置近效期阈值
                    enrichGoodsWithValidPeriodThreshold(auctionSpuMap, task.getPlatformOrgId(),validPeriodThresholds);
                    List<String> validatedGoods = validateStoreGoods(storeGoods, auctionSpuMap, storeId, task.getTaskCode());
                    validatedStoreGoodsMap.put(storeId, validatedGoods);
                    totalValidatedGoods += validatedGoods.size();
                    totalFilteredGoods += (storeGoods.size() - validatedGoods.size());
                }
            }
            logger.info("商品可退性验证完成，验证通过：{}个，过滤：{}个，任务编码：{}",
                    totalValidatedGoods, totalFilteredGoods, task.getTaskCode());
            return validatedStoreGoodsMap;
        } catch (Exception e) {
            logger.error("验证商品可退性失败，任务编码：{}", task.getTaskCode(), e);
            // 验证失败时返回原始数据，不阻断流程
            logger.warn("商品可退性验证失败，使用原始商品列表继续处理，任务编码：{}", task.getTaskCode());
            return storeGoodsMap;
        }
    }

    /**
     * 验证单个门店的商品可退性
     */
    private List<String> validateStoreGoods(List<String> storeGoods, Map<String, AuctionSpuBaseInfo> auctionSpuMap, Long storeId, String taskCode) {
        List<String> validatedGoods = new ArrayList<>();
        for (String goodsNo : storeGoods) {
            try {
                if (isGoodsReturnable(goodsNo, auctionSpuMap)) {
                    validatedGoods.add(goodsNo);
                } else {
                    logger.debug("商品{}不满足可退条件，门店：{}，任务编码：{}", goodsNo, storeId, taskCode);
                }
            } catch (Exception e) {
                logger.warn("验证商品{}可退性异常，门店：{}，任务编码：{}，错误：{}", goodsNo, storeId, taskCode, e.getMessage());
                // 验证异常时，保守处理：不加入可退列表
            }
        }
        logger.debug("门店{}商品验证完成，原始：{}个，可退：{}个，任务编码：{}",
                storeId, storeGoods.size(), validatedGoods.size(), taskCode);

        return validatedGoods;
    }

    /**
     * 判断商品是否可退
     * 验证条件：退仓说明=可退仓 AND 返厂说明=无条件可退
     */
    private boolean isGoodsReturnable(String goodsNo, Map<String, AuctionSpuBaseInfo> auctionSpuMap) {
        AuctionSpuBaseInfo spuInfo = auctionSpuMap.get(goodsNo);
        if (spuInfo == null) {
            logger.debug("商品{}未找到SPU信息", goodsNo);
            return false;
        }

        OtherProperty otherProperty = spuInfo.getOtherProperty();
        if (otherProperty == null) {
            logger.debug("商品{}未找到其他属性信息", goodsNo);
            return false;
        }

        String rtreport = otherProperty.getRtreport(); // 退仓说明
        String rereport = otherProperty.getRereport(); // 返厂说明

        // 验证条件：退仓说明=可退仓 AND 返厂说明=无条件可退
        boolean isReturnableToWarehouse = "可退仓".equals(rtreport);
        boolean isReturnableToFactory = "无条件可退".equals(rereport);

        boolean isReturnable = isReturnableToWarehouse && isReturnableToFactory;

        if (!isReturnable) {
            logger.debug("商品{}不满足可退条件，退仓说明：{}，返厂说明：{}", goodsNo, rtreport, rereport);
        }

        return isReturnable;
    }


    /**
     * 为商品设置近效期阈值
     * 根据商品的有效期（validperiod）匹配对应的效期标准区间，获取相应的阈值
     */
       /**
     * 为商品设置近效期阈值到上下文中
     */
    private void enrichGoodsWithValidPeriodThreshold(Map<String, AuctionSpuBaseInfo> auctionSpuMap, Long platformOrgId, Map<String, Integer> validPeriodThresholds) {
        try {
            Map<String, Integer> goodsThresholdMap = new HashMap<>();

            // 为每个商品计算对应的近效期阈值
            for (Map.Entry<String, AuctionSpuBaseInfo> entry : auctionSpuMap.entrySet()) {
                String goodsNo = entry.getKey();
                AuctionSpuBaseInfo spuInfo = entry.getValue();

                try {
                    Integer threshold = calculateValidPeriodThreshold(spuInfo.getValidperiod(), validPeriodThresholds);
                    Integer finalThreshold = Objects.isNull(threshold) ? 12 : threshold;

                    // 设置到上下文中而不是直接设置到对象上
                    goodsThresholdMap.put(goodsNo, finalThreshold);

                    logger.debug("商品{}计算近效期阈值：{}个月，有效期：{}", goodsNo, finalThreshold, spuInfo.getValidperiod());
                } catch (Exception e) {
                    logger.warn("为商品{}计算近效期阈值失败，有效期：{}，使用默认阈值12，错误：{}", goodsNo, spuInfo.getValidperiod(), e.getMessage());
                    // 异常情况下使用默认阈值
                    goodsThresholdMap.put(goodsNo, 12);
                }
            }

            // 批量设置到上下文中
            GoodsThresholdContext.setGoodsThresholds(goodsThresholdMap);
            logger.info("商品近效期阈值设置到上下文完成，商品数量：{}，平台ID：{}，上下文状态：{}",
                    auctionSpuMap.size(), platformOrgId, GoodsThresholdContext.getContextInfo());

        } catch (Exception e) {
            logger.error("设置商品近效期阈值到上下文失败，平台ID：{}", platformOrgId, e);
        }
    }

    /**
     * 获取效期标准配置
     */
    private Map<String, Integer> getValidPeriodThresholds(Long platformOrgId) {
        Map<String, Integer> thresholds = new HashMap<>();
        try {
            // 从 resultMap 中提取效期标准配置
            RuleParam ruleParam = new RuleParam();
            ruleParam.setConfigType(ConfigTypeEnum.BLCK.getType().toString());
            ruleParam.setOrgId(platformOrgId);
            ruleParam.setScopeCode(DicApiEnum.BLKC_4_3.getCode());
            ResponseEntity<CommonRes<AmisMap>> ruleResponse = scibFeignClient.getRuleList(ruleParam);
            if (!isValidResponse(ruleResponse)) {
                logger.warn("获取效期标准配置失败，平台ID：{}", platformOrgId);
                return thresholds;
            }
            CommonRes<AmisMap> commonRes = ruleResponse.getBody();
            if (commonRes != null && Constants.INT_ZERO.equals(commonRes.getStatus())) {
                AmisMap amisMap = commonRes.getData();
                if (amisMap != null && amisMap.containsKey("resultMap")) {
                    Object resultMapObj = amisMap.get("resultMap");
                    if (resultMapObj instanceof HashMap) {
                        HashMap resultMap = (HashMap) resultMapObj;
                        // 提取各个效期标准配置
                        extractValidPeriodThreshold(resultMap, "blkc_4_3_3", thresholds); // 有效期≤12个月
                        extractValidPeriodThreshold(resultMap, "blkc_4_3_4", thresholds); // 12个月<有效期≤18个月
                        extractValidPeriodThreshold(resultMap, "blkc_4_3_5", thresholds); // 18个月<有效期≤36个月
                        extractValidPeriodThreshold(resultMap, "blkc_4_3_6", thresholds); // 有效期>36个月
                    }
                }
            }

        } catch (Exception e) {
            logger.error("获取效期标准配置异常，平台ID：{}", platformOrgId, e);
        }

        return thresholds;
    }

    /**
     * 提取单个效期标准配置
     */
    private void extractValidPeriodThreshold(HashMap resultMap, String dictCode, Map<String, Integer> thresholds) {
        try {
            if (resultMap.containsKey(dictCode)) {
                Object configObj = resultMap.get(dictCode);
                if (configObj instanceof RuleDetailDTO) {
                    RuleDetailDTO config = (RuleDetailDTO) configObj;
                    if (config.getPerprotyValue() != null) {
                        thresholds.put(dictCode, NumberUtil.parseInt(config.getPerprotyValue())>0?NumberUtil.parseInt(config.getPerprotyValue()):12);
                        logger.debug("提取效期标准配置：{}，阈值：{}个月", dictCode, config.getPerprotyValue());
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("提取效期标准配置失败，dictCode：{}", dictCode, e);
        }
    }

    /**
     * 根据商品有效期计算对应的近效期阈值
     */
    private Integer calculateValidPeriodThreshold(String validperiod, Map<String, Integer> validPeriodThresholds) {
        try {
           // 效期月份=validperiod/30 ，向上取整效期单位=天，效期单位三月，效期月份 = validperiod 。效期单位 为空 或者 validperiod 为空， 效期月份 按照"有效期>36个月”计。效期月份 这个值 ，套入 我们那个参数里面，取 对应的 月份
            // 计算有效期月份数
            int validPeriodMonths = parseValidPeriodToMonths(validperiod);
            // 根据有效期区间匹配对应的阈值
            if (validPeriodMonths <= 12) {
                // 有效期≤12个月
                return validPeriodThresholds.get("blkc_4_3_3");
            } else if (validPeriodMonths <= 18) {
                // 12个月<有效期≤18个月
                return validPeriodThresholds.get("blkc_4_3_4");
            } else if (validPeriodMonths <= 36) {
                // 18个月<有效期≤36个月
                return validPeriodThresholds.get("blkc_4_3_5");
            } else {
                // 有效期>36个月
                return validPeriodThresholds.get("blkc_4_3_6");
            }
        } catch (Exception e) {
            logger.warn("计算有效期阈值失败：{}，使用默认阈值", validperiod, e);
            // 异常情况下使用最大阈值
            return validPeriodThresholds.get("blkc_4_3_6");
        }
    }

    /**
     * 解析有效期字符串为月份数
     * 支持格式：
     * - "24月" → 24个月
     * - "720天" → 24个月（720/30向上取整）
     * - 空值或无效格式 → 99个月（默认值）
     */
    private int parseValidPeriodToMonths(String validperiod) {
        // 空值处理：默认为99月
        if (!StringUtils.hasText(validperiod)) {
            logger.debug("有效期为空，使用默认值99月");
            return 99;
        }

        String trimmedPeriod = validperiod.trim();

        try {
            // 提取数字部分
            String numStr = trimmedPeriod.replaceAll("[^0-9]", "");
            if (!StringUtils.hasText(numStr)) {
                logger.debug("无法解析有效期数值：{}，使用默认值99月", validperiod);
                return 99;
            }

            int numValue = Integer.parseInt(numStr);

            // 判断单位并转换为月份
            if (trimmedPeriod.contains("天")) {
                // 效期单位 = 天，效期月份 = validperiod / 30，向上取整
                int months = (int) Math.ceil((double) numValue / 30);
                logger.debug("有效期{}天转换为{}月", numValue, months);
                return months;
            } else if (trimmedPeriod.contains("月")) {
                // 效期单位 = 月，效期月份 = validperiod
                logger.debug("有效期{}月", numValue);
                return numValue;
            } else {
                // 效期单位为空，认为是99月
                logger.debug("有效期单位为空：{}，使用默认值99月", validperiod);
                return 99;
            }

        } catch (NumberFormatException e) {
            logger.warn("解析有效期数值失败：{}，使用默认值99月", validperiod, e);
            return 99;
        }
    }

    /**
     * 计算退仓数量
     * 包括近效期判断和保留库存计算
     */
    private ReturnCalculationResult calculateReturnQuantity(OrgInfoBaseCache store , String goodsNo,
                                                           Map<String, List<StockGoodsBatchCodeSimpleInfo>> batchMap,
                                                           String taskCode, Map<String, JymlStoreSkuSuggest> suggestMap,TokenUserDTO userDTO ) {
        ReturnCalculationResult result = new ReturnCalculationResult();
        result.setStoreId(store.getOutId());
        result.setGoodsNo(goodsNo);

        try {
            // 1. 获取商品的近效期阈值
            Integer threshold = GoodsThresholdContext.getGoodsThreshold(goodsNo);
            // 2. 分析所有批次，区分近效期和非近效期
            BatchAnalysisResult batchAnalysis = analyzeBatches(batchMap, threshold, goodsNo);
            // 3. 近效期库存全部退仓
            result.setNearExpiryQuantity(batchAnalysis.getNearExpiryQuantity());
            result.setNearExpiryBatchReturnMap(batchAnalysis.getNearExpiryBatchQuantitiesMap());
            // 4. 计算非近效期库存的保留数量
            RetentionCalculationResult retentionResult = calculateRetentionQuantity(store, goodsNo, batchAnalysis, suggestMap);

            result.setRetainQuantity(retentionResult.getRetainQuantity());
            result.setNonExpiryReturnQuantity(retentionResult.getReturnQuantity());
            result.setNonExpiryBatchReturnMap(batchAnalysis.getNonExpiryBatchQuantitiesMap());

            result.setIsSeasonalProduct(retentionResult.getIsSeasonalProduct());
            result.setCalculationNote(retentionResult.getCalculationNote());

            // 5. 计算总数量
            result.setTotalStockQuantity(batchAnalysis.getTotalQuantity());
            result.setTotalReturnQuantity(result.getNearExpiryQuantity().add(result.getNonExpiryReturnQuantity()));
            logger.debug("门店{}商品{}退仓计算完成：总库存{}，近效期退仓{}，非近效期退仓{}，保留{}，阈值{}月",
                    store.getOutId(), goodsNo, result.getTotalStockQuantity(), result.getNearExpiryQuantity(),
                    result.getNonExpiryReturnQuantity(), result.getRetainQuantity(), threshold);
            return result;
        } catch (Exception e) {
            logger.error("计算门店{}商品{}退仓数量失败，任务编码：{}", store.getOutId(), goodsNo, taskCode, e);
            // 异常情况下返回空结果
            result.setNearExpiryQuantity(BigDecimal.ZERO);
            result.setNonExpiryReturnQuantity(BigDecimal.ZERO);
            result.setTotalReturnQuantity(BigDecimal.ZERO);
            result.setRetainQuantity(BigDecimal.ZERO);
            result.setTotalStockQuantity(BigDecimal.ZERO);
            result.setCalculationNote("计算异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 按门店维度生成退仓执行单（包含主单和明细）
     * 一次性处理当前门店的所有退仓商品，生成统一的主单和对应的明细
     *
     * @param store 门店信息
     * @param storeReturnResults 门店所有商品的退仓计算结果 Map<goodsNo, ReturnCalculationResult>
     * @param goodsMap 商品信息映射
     * @param stockMap 库存信息映射
     * @param auctionSpuBaseMap 采购信息映射
     * @param reasonMap 退仓原因映射
     * @param userDTO 操作用户信息
     */
    private void generateStoreReturnExecuteOrder(OrgInfoBaseCache store,
                                                 Map<String, ReturnCalculationResult> storeReturnResults,
                                                 Map<String, SpuListVo> goodsMap,
                                                 Map<String, List<StockGoodsBatchCodeSimpleInfo>> stockMap,
                                                 Map<String, AuctionSpuBaseInfo> auctionSpuBaseMap,
                                                 Map<String, String> reasonMap,
                                                 TokenUserDTO userDTO) {

        logger.info("开始为门店:{}生成退仓执行单", store.getOutId());
        // 过滤出有退仓数量的商品
        Map<String, ReturnCalculationResult> validReturnResults = storeReturnResults.entrySet().stream()
                .filter(entry -> entry.getValue().getTotalReturnQuantity().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (validReturnResults.isEmpty()) {
            logger.info("门店:{}无需退仓商品，跳过生成执行单", store.getOutId());
            return;
        }
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        int createdMonth = calendar.get(Calendar.YEAR) * 100 + (calendar.get(Calendar.MONTH) + 1);
        // 生成所有商品的退仓明细
        List<IscmStoreReturnExecuteOrderDetail> allExecuteOrderDetails = new ArrayList<>();
        for (Map.Entry<String, ReturnCalculationResult> entry : validReturnResults.entrySet()) {
            String goodsNo = entry.getKey();
            SpuListVo spuListVo = goodsMap.get(goodsNo);
            List<StockGoodsBatchCodeSimpleInfo> batchStocks = stockMap.get(goodsNo);
            Map<String, StockGoodsBatchCodeSimpleInfo> batchCodeSimpleInfoMap = batchStocks.stream().collect(Collectors.toMap(StockGoodsBatchCodeSimpleInfo::getBatchNo, Function.identity(), (k1, k2) -> k1));
            Map<String, List<StockGoodsBatchCodeSimpleInfo>> allBatchMap = batchStocks.stream().collect(Collectors.groupingBy(StockGoodsBatchCodeSimpleInfo::getBatchNo));
            storeReturnResults.get(goodsNo).getNearExpiryBatchReturnMap().forEach((batchNo, quantity) -> {
                //allExecuteOrderDetails.add(createExecuteOrderDetail(store, goodsNo, batchNo, quantity, "近效期", reasonMap, auctionSpuBaseMap, stockMap, createdMonth, now, userDTO));
                IscmStoreReturnExecuteOrderDetail detail = new IscmStoreReturnExecuteOrderDetail();
                // 基本信息
                detail.setReturnBusinessType(ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN.getCode());
                detail.setPlatformOrgId(store.getPlatformOrgId());
                detail.setPlatformOrgName(store.getPlatformShortName());
                detail.setCompanyOrgId(store.getBusinessOrgId());
                detail.setCompanyCode(store.getBusinessSapCode());
                detail.setCompanyName(store.getBusinessShortName());
                detail.setStoreOrgId(store.getId());
                detail.setStoreCode(store.getSapCode());
                detail.setStoreName(store.getShortName());
                detail.setWarehouseCode(""); // 暂时为空
                detail.setWarehouseName("");

                // 商品信息
                detail.setGoodsNo(goodsNo);
                detail.setGoodsName(spuListVo.getName());
                detail.setBarCode(spuListVo.getBarCode());
                detail.setGoodsCommonName(spuListVo.getCurName());
                detail.setGoodsUnit(spuListVo.getUnit());
                detail.setDescription(spuListVo.getDescription());
                detail.setSpecifications(spuListVo.getJhiSpecification());
                detail.setDosageForm(spuListVo.getDosageForms());
                detail.setManufacturer(spuListVo.getProducter());
                detail.setApprovalNumber(spuListVo.getApprdocno());
                detail.setGoodsClassId(Long.valueOf(spuListVo.getCategoryId()));
                detail.setGoodsClassName("");

                // 批次信息
                StockGoodsBatchCodeSimpleInfo batchStock = batchCodeSimpleInfoMap.get(batchNo);
                detail.setBatchNo(batchStock.getBatchNo());
                detail.setValidityDate(batchStock.getExpireDate());
                detail.setProduceDate(batchStock.getProduceDate());
                detail.setValidityDays(batchStock.getExpireDate() == null ? 0 :Math.abs(DateUtils.getDifferDays(batchStock.getExpireDate(), new Date())));

                // 库存信息
                BigDecimal goodsUseStock = allBatchMap.get(goodsNo).stream()
                        .map(stock -> stock.getStock()
                                .subtract(stock.getWaitStock().add(stock.getUnqualifiedAreaStock()))
                                .add(stock.getTransitStock()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal batchUseStock = allBatchMap.get(goodsNo).stream().filter(v->batchNo.equals(v.getBatchNo()))
                        .map(stock -> stock.getStock()
                                .subtract(stock.getWaitStock().add(stock.getUnqualifiedAreaStock()))
                                .add(stock.getTransitStock()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                detail.setGoodsStock(goodsUseStock);
                detail.setBatchStock(batchUseStock);

                // 退仓数量信息
                detail.setShouldReturnQuantity(quantity);
                detail.setReturnQuantity(quantity);
                detail.setRealReturnQuantity(quantity);
                detail.setIssueReturnQuantity(quantity);
                detail.setIssueReturnAmount(BigDecimal.ZERO);
                // 退仓原因
                detail.setReturnReasonType(ISCM_DEFECTIVE_RETURN_REASON_DEFAULT);
                if (!reasonMap.containsKey(Constants.ISCM_DEFECTIVE_RETURN_REASON_DEFAULT)) {
                    reasonMap.put(Constants.ISCM_DEFECTIVE_RETURN_REASON_DEFAULT, Constants.ISCM_DEFECTIVE_RETURN_REASON_NAME_DEFAULT);
                }
                detail.setReturnReason(reasonMap.get(ISCM_DEFECTIVE_RETURN_REASON_DEFAULT));
                // 默认值
                detail.setForecastSales(BigDecimal.ZERO);
                detail.setStockLowerLimitDays(0);
                detail.setStockUpperLimitDays(0);
                detail.setHdSynthesizeAverageDailySales(BigDecimal.ZERO);
                detail.setStockUpperLimit(BigDecimal.ZERO);
                detail.setStockLowerLimit(BigDecimal.ZERO);
                detail.setBdpSynthesizeAverageDailySales(BigDecimal.ZERO);
                detail.setStorageDays(0);
                detail.setThirtySalesCount(0);
                detail.setThirtySalesQuantity(BigDecimal.ZERO);
                detail.setMinDisplayQuantity(BigDecimal.ZERO);
                detail.setNonSalesDays(0);
                detail.setGoodsline("");
                detail.setPushlevel("");
                detail.setForbidDistribute("");
                detail.setForbidReturnWarehouse("");
                detail.setForbidApply("");
                detail.setForbidAllot("");
                detail.setPosReturnOrderNo("");
                detail.setRowNo("");

                // 采购信息
                AuctionSpuBaseInfo auctionSpuBaseInfo = auctionSpuBaseMap.get(goodsNo);
                if (auctionSpuBaseInfo != null) {
                    detail.setGoodsPurChannel(auctionSpuBaseInfo.getPurchchannel());
                    detail.setPurchaseGroup(auctionSpuBaseInfo.getEkgrp());
                    detail.setPurchaseOrgCode(auctionSpuBaseInfo.getOrderorg());
                    detail.setPurchaseOrgName("");
                }
                // 创建信息
                detail.setCreatedMonth(createdMonth);
                detail.setCreatedBy(userDTO.getUserId());
                detail.setCreatedName(userDTO.getName());
                detail.setUpdatedBy(userDTO.getUserId());
                detail.setUpdatedName(userDTO.getName());
                detail.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                detail.setGmtCreate(now);
                detail.setGmtUpdate(now);
                allExecuteOrderDetails.add(detail);
            });

            storeReturnResults.get(goodsNo).getNonExpiryBatchReturnMap().forEach((batchNo, quantity) -> {
                //allExecuteOrderDetails.add(createExecuteOrderDetail(store, goodsNo, batchNo, quantity, "近效期", reasonMap, auctionSpuBaseMap, stockMap, createdMonth, now, userDTO));
                IscmStoreReturnExecuteOrderDetail detail = new IscmStoreReturnExecuteOrderDetail();
                // 基本信息
                detail.setReturnBusinessType(ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN.getCode());
                detail.setPlatformOrgId(store.getPlatformOrgId());
                detail.setPlatformOrgName(store.getPlatformShortName());
                detail.setCompanyOrgId(store.getBusinessOrgId());
                detail.setCompanyCode(store.getBusinessSapCode());
                detail.setCompanyName(store.getBusinessShortName());
                detail.setStoreOrgId(store.getId());
                detail.setStoreCode(store.getSapCode());
                detail.setStoreName(store.getShortName());
                detail.setWarehouseCode(""); // 暂时为空
                detail.setWarehouseName("");

                // 商品信息
                detail.setGoodsNo(goodsNo);
                detail.setGoodsName(spuListVo.getName());
                detail.setBarCode(spuListVo.getBarCode());
                detail.setGoodsCommonName(spuListVo.getCurName());
                detail.setGoodsUnit(spuListVo.getUnit());
                detail.setDescription(spuListVo.getDescription());
                detail.setSpecifications(spuListVo.getJhiSpecification());
                detail.setDosageForm(spuListVo.getDosageForms());
                detail.setManufacturer(spuListVo.getProducter());
                detail.setApprovalNumber(spuListVo.getApprdocno());
                detail.setGoodsClassId(Long.valueOf(spuListVo.getCategoryId()));
                detail.setGoodsClassName("");
                // 批次信息
                StockGoodsBatchCodeSimpleInfo batchStock = batchCodeSimpleInfoMap.get(batchNo);
                detail.setBatchNo(batchStock.getBatchNo());
                detail.setValidityDate(batchStock.getExpireDate());
                detail.setProduceDate(batchStock.getProduceDate());
                detail.setValidityDays(batchStock.getExpireDate() == null ? 0 :Math.abs(DateUtils.getDifferDays(batchStock.getExpireDate(), new Date())));
                // 库存信息
                BigDecimal goodsUseStock = allBatchMap.get(goodsNo).stream()
                        .map(stock -> stock.getStock()
                                .subtract(stock.getWaitStock().add(stock.getUnqualifiedAreaStock()))
                                .add(stock.getTransitStock()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal batchUseStock = allBatchMap.get(goodsNo).stream().filter(v->batchNo.equals(v.getBatchNo()))
                        .map(stock -> stock.getStock()
                                .subtract(stock.getWaitStock().add(stock.getUnqualifiedAreaStock()))
                                .add(stock.getTransitStock()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                detail.setGoodsStock(goodsUseStock);
                detail.setBatchStock(batchUseStock);

                // 退仓数量信息
                detail.setShouldReturnQuantity(quantity);
                detail.setReturnQuantity(quantity);
                detail.setRealReturnQuantity(quantity);
                detail.setIssueReturnQuantity(quantity);
                detail.setIssueReturnAmount(BigDecimal.ZERO);
                // 退仓原因
                detail.setReturnReasonType(ISCM_DEFECTIVE_RETURN_REASON_NON_EXPIRY);
                detail.setReturnReason(reasonMap.get(ISCM_DEFECTIVE_RETURN_REASON_DEFAULT));
                // 默认值
                detail.setForecastSales(BigDecimal.ZERO);
                detail.setStockLowerLimitDays(0);
                detail.setStockUpperLimitDays(0);
                detail.setHdSynthesizeAverageDailySales(BigDecimal.ZERO);
                detail.setStockUpperLimit(BigDecimal.ZERO);
                detail.setStockLowerLimit(BigDecimal.ZERO);
                detail.setBdpSynthesizeAverageDailySales(BigDecimal.ZERO);
                detail.setStorageDays(0);
                detail.setThirtySalesCount(0);
                detail.setThirtySalesQuantity(BigDecimal.ZERO);
                detail.setMinDisplayQuantity(BigDecimal.ZERO);
                detail.setNonSalesDays(0);
                detail.setGoodsline("");
                detail.setPushlevel("");
                detail.setForbidDistribute("");
                detail.setForbidReturnWarehouse("");
                detail.setForbidApply("");
                detail.setForbidAllot("");
                detail.setPosReturnOrderNo("");
                detail.setRowNo("");

                // 采购信息
                AuctionSpuBaseInfo auctionSpuBaseInfo = auctionSpuBaseMap.get(goodsNo);
                if (auctionSpuBaseInfo != null) {
                    detail.setGoodsPurChannel(auctionSpuBaseInfo.getPurchchannel());
                    detail.setPurchaseGroup(auctionSpuBaseInfo.getEkgrp());
                    detail.setPurchaseOrgCode(auctionSpuBaseInfo.getOrderorg());
                    detail.setPurchaseOrgName("");
                }
                // 创建信息
                detail.setCreatedMonth(createdMonth);
                detail.setCreatedBy(userDTO.getUserId());
                detail.setCreatedName(userDTO.getName());
                detail.setUpdatedBy(userDTO.getUserId());
                detail.setUpdatedName(userDTO.getName());
                detail.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
                detail.setGmtCreate(now);
                detail.setGmtUpdate(now);
                allExecuteOrderDetails.add(detail);
            });
        }
        if (CollectionUtils.isEmpty(allExecuteOrderDetails)) {
            logger.info("门店:{}没有需要新增的退仓明细", store.getOutId());
            return;
        }
        // 获取或创建门店统一的订单号
        String orderNo  = genOrderNo("", store.getSapCode());
        // 为所有明细设置订单号
        allExecuteOrderDetails.forEach(detail -> detail.setReturnOrderNo(orderNo));
        // 批量插入所有明细
        logger.info("门店:{}开始批量插入{}条退仓明细", store.getOutId(), allExecuteOrderDetails.size());
        Lists.partition(allExecuteOrderDetails, Constants.BATCH_INSERT_ONCE_MAX_VALUE)
                .forEach(batch -> {
                    try {
                        iscmStoreReturnExecuteOrderDetailExtendMapper.batchInsert(batch);
                        logger.info("批量插入{}条明细成功", batch.size());
                    } catch (Exception e) {
                        logger.error("批量插入明细失败", e);
                        throw new RuntimeException("批量插入退仓明细失败", e);
                    }
                });
        // 生成或更新门店主单
        generateOrUpdateStoreMainOrder(store, orderNo, allExecuteOrderDetails, createdMonth, now, userDTO);
        logger.info("门店:{}退仓执行单生成完成，订单号:{}, 明细数量:{}, 涉及商品数量:{}",store.getOutId(), orderNo, allExecuteOrderDetails.size(), validReturnResults.size());
    }
    /**
     * 生成或更新门店主单
     */
    private void generateOrUpdateStoreMainOrder(OrgInfoBaseCache store, String orderNo,
                                                List<IscmStoreReturnExecuteOrderDetail> allDetails,
                                                int createdMonth, Date now, TokenUserDTO userDTO) {

        // 查询现有主单
        IscmStoreReturnExecuteOrderMainExample mainExample = new IscmStoreReturnExecuteOrderMainExample();
        mainExample.createCriteria().andReturnOrderNoEqualTo(orderNo);
        List<IscmStoreReturnExecuteOrderMain> existingMains = iscmStoreReturnExecuteOrderMainMapper.selectByExample(mainExample);
        if (CollectionUtils.isEmpty(existingMains)) {
            // 创建新主单
            IscmStoreReturnExecuteOrderMain mainOrder = createStoreMainOrder( store, orderNo, allDetails, createdMonth, now, userDTO);
            try {
                iscmStoreReturnExecuteOrderMainExtendMapper.batchInsert(Lists.newArrayList(mainOrder));
                logger.info("门店:{}创建新主单完成，订单号:{}", store.getOutId(), orderNo);
            } catch (Exception e) {
                logger.error("创建主单失败，订单号:{}", orderNo, e);
                throw new RuntimeException("创建门店退仓主单失败", e);
            }
        } else {
            // 更新现有主单
            IscmStoreReturnExecuteOrderMain existingMain = existingMains.get(0);
            updateStoreMainOrder(existingMain, orderNo, now, userDTO);
            try {
                iscmStoreReturnExecuteOrderMainExtendMapper.batchUpdate(Lists.newArrayList(existingMain), createdMonth);
                logger.info("门店:{}更新主单完成，订单号:{}", store.getOutId(), orderNo);
            } catch (Exception e) {
                logger.error("更新主单失败，订单号:{}", orderNo, e);
                throw new RuntimeException("更新门店退仓主单失败", e);
            }
        }
    }

    /**
     * 创建门店主单
     */
    private IscmStoreReturnExecuteOrderMain createStoreMainOrder(OrgInfoBaseCache store, String orderNo,
                                                                 List<IscmStoreReturnExecuteOrderDetail> allDetails,
                                                                 int createdMonth, Date now, TokenUserDTO userDTO) {
        IscmStoreReturnExecuteOrderMain mainOrder = new IscmStoreReturnExecuteOrderMain();
        // 从第一个明细复制基本信息
        IscmStoreReturnExecuteOrderDetail firstDetail = allDetails.get(0);
        BeanUtils.copyProperties(firstDetail, mainOrder);
        // 设置主单特有字段
        mainOrder.setReturnOrderNo(orderNo);
        mainOrder.setReturnType((int)ReturnWarehouseBusinessTypeEnum.UNMANAGE_GOODS_RETURN.getCode());
        mainOrder.setWarehouseCode(""); // 仓库代码为空
        mainOrder.setWarehouseName("");
        mainOrder.setPosReturnOrderNo("");
        mainOrder.setCreatedBy(userDTO.getUserId());
        mainOrder.setCreatedName(userDTO.getName());
        mainOrder.setUpdatedBy(userDTO.getUserId());
        mainOrder.setUpdatedName(userDTO.getName());
        mainOrder.setCreatedMonth(createdMonth);
        mainOrder.setProcessStatus(StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode());
        mainOrder.setProcessStatusAll("|" + StoreReturnExecuteProcessStatusEnum.NON_APPROVED.getCode() + "|");
        mainOrder.setGmtCreate(now);
        mainOrder.setGmtUpdate(now);
        mainOrder.setIssueValidityDays(0);

        // 汇总所有明细的数量信息
        StoreReturnExecuteOrderSum orderSum = new StoreReturnExecuteOrderSum();
        List<StoreReturnExecuteOrderQty> orderQtyList = new ArrayList<>();
        BeanUtils.copyList(allDetails, orderQtyList, StoreReturnExecuteOrderQty.class);
        orderSum.sum(orderQtyList, false);
        BeanUtils.copyProperties(orderSum, mainOrder);
        logger.info("门店:{}主单汇总信息 - 总退仓数量:{}, 明细条数:{}", store.getOutId(), mainOrder.getRealReturnQuantityTotal(), allDetails.size());
        return mainOrder;
    }

    /**
     * 更新门店主单
     */
    private void updateStoreMainOrder(IscmStoreReturnExecuteOrderMain existingMain, String orderNo,
                                      Date now, TokenUserDTO userDTO) {

        // 重新查询该订单号下的所有明细进行汇总
        IscmStoreReturnExecuteOrderDetailExample detailExample = new IscmStoreReturnExecuteOrderDetailExample();
        detailExample.createCriteria().andReturnOrderNoEqualTo(orderNo);
        List<IscmStoreReturnExecuteOrderDetail> allDetails =iscmStoreReturnExecuteOrderDetailMapper.selectByExample(detailExample);
        // 重新汇总数量信息
        StoreReturnExecuteOrderSum orderSum = new StoreReturnExecuteOrderSum();
        List<StoreReturnExecuteOrderQty> orderQtyList = new ArrayList<>();
        BeanUtils.copyList(allDetails, orderQtyList, StoreReturnExecuteOrderQty.class);
        orderSum.sum(orderQtyList, false);
        BeanUtils.copyProperties(orderSum, existingMain);
        // 更新时间和操作人
        existingMain.setUpdatedBy(userDTO.getUserId());
        existingMain.setUpdatedName(userDTO.getName());
        existingMain.setGmtUpdate(now);
        logger.info("订单号:{}主单更新汇总信息 - 总退仓数量:{}, 明细条数:{}", orderNo, existingMain.getRealReturnQuantityTotal(), allDetails.size());
    }

    /**
     * 分析批次，区分近效期和非近效期
     */
    private BatchAnalysisResult analyzeBatches(Map<String, List<StockGoodsBatchCodeSimpleInfo>> batchMap,
                                              Integer threshold, String goodsNo) {
        BatchAnalysisResult analysisResult = new BatchAnalysisResult();
        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal nearExpiryQuantity = BigDecimal.ZERO;
        BigDecimal nonExpiryQuantity = BigDecimal.ZERO;
        List<String> nearExpiryBatches = new ArrayList<>();
        List<String> nonExpiryBatches = new ArrayList<>();
        Map<String, BigDecimal> nonExpiryBatchQuantitiesMap = new HashMap<>();
        Map<String, BigDecimal> nearExpiryBatchQuantitiesMap = new HashMap<>();
        for (Map.Entry<String, List<StockGoodsBatchCodeSimpleInfo>> entry : batchMap.entrySet()) {
            String batchNo = entry.getKey();
            List<StockGoodsBatchCodeSimpleInfo> stocks = entry.getValue();
            for (StockGoodsBatchCodeSimpleInfo stock : stocks) {
                BigDecimal useStock  = stock.getStock().subtract(stock.getWaitStock().add(stock.getUnqualifiedAreaStock())).add(stock.getTransitStock());
                totalQuantity=totalQuantity.add(useStock);
                // 判断是否近效期
                boolean isNearExpiry = isNearExpiry(stock, threshold, goodsNo);
                if (isNearExpiry) {
                    nearExpiryQuantity=nearExpiryQuantity.add(useStock);
                    if (!nearExpiryBatches.contains(batchNo)) {
                        nearExpiryBatches.add(batchNo);
                    }
                    nearExpiryBatchQuantitiesMap.put(batchNo, nonExpiryBatchQuantitiesMap.getOrDefault(batchNo, BigDecimal.ZERO).add(useStock));
                    logger.debug("商品{}批号{}为近效期，数量：{}，有效期至：{}", goodsNo, batchNo, useStock, stock.getExpireDate());
                } else {
                    nonExpiryQuantity=nonExpiryQuantity.add(useStock);
                    if (!nonExpiryBatches.contains(batchNo)) {
                        nonExpiryBatches.add(batchNo);
                    }
                    nonExpiryBatchQuantitiesMap.put(batchNo, nonExpiryBatchQuantitiesMap.getOrDefault(batchNo, BigDecimal.ZERO).add(useStock));
                    logger.debug("商品{}批号{}为非近效期，数量：{}，有效期至：{}",goodsNo, batchNo, useStock, stock.getExpireDate());
                }
            }
        }
        analysisResult.setTotalQuantity(totalQuantity);
        analysisResult.setNearExpiryQuantity(nearExpiryQuantity);
        analysisResult.setNonExpiryQuantity(nonExpiryQuantity);
        analysisResult.setNearExpiryBatches(nearExpiryBatches);
        analysisResult.setNonExpiryBatches(nonExpiryBatches);
        analysisResult.setNonExpiryBatchQuantitiesMap(nonExpiryBatchQuantitiesMap);
        analysisResult.setNearExpiryBatchQuantitiesMap(nearExpiryBatchQuantitiesMap);
        return analysisResult;
    }

    /**
     * 判断批次是否近效期
     */
    private boolean isNearExpiry(StockGoodsBatchCodeSimpleInfo stock, Integer threshold, String goodsNo) {
        try {
            Date expireDate = stock.getExpireDate();
            if (expireDate == null) {
                logger.debug("商品{}批号{}没有有效期信息，视为非近效期", goodsNo, stock.getBatchNo());
                return false;
            }

            Date currentDate = new Date();

            // 如果已经过期，视为近效期
            if (expireDate.before(currentDate)) {
                logger.debug("商品{}批号{}已过期，视为近效期", goodsNo, stock.getBatchNo());
                return true;
            }

            // 计算剩余天数
            long diffInMillies = expireDate.getTime() - currentDate.getTime();
            long remainingDays = diffInMillies / (24 * 60 * 60 * 1000);

            // 转换为月数（向上取整）
            double remainingMonths = Math.ceil((double) remainingDays / 30);

            // 与阈值比较
            boolean isNear = remainingMonths <= threshold;

            logger.debug("商品{}批号{}剩余{}天({}月)，阈值{}月，是否近效期：{}",
                    goodsNo, stock.getBatchNo(), remainingDays, remainingMonths, threshold, isNear);

            return isNear;

        } catch (Exception e) {
            logger.warn("判断商品{}批号{}近效期失败，视为非近效期", goodsNo, stock.getBatchNo(), e);
            return false;
        }
    }

    /**
     * 计算非近效期库存的保留数量
     */
    private RetentionCalculationResult calculateRetentionQuantity(OrgInfoBaseCache store, String goodsNo,
                                                                  BatchAnalysisResult batchAnalysis,
                                                                  Map<String, JymlStoreSkuSuggest> suggestMap) {
        RetentionCalculationResult result = new RetentionCalculationResult();

        try {
            // 1. 查询商品的销售建议数据
            JymlStoreSkuSuggest suggest = suggestMap.get(goodsNo);
            if (suggest == null) {
                // 选配目录里查不到商品门店，保留数量=0
                result.setRetainQuantity(BigDecimal.ZERO);
                result.setReturnQuantity(batchAnalysis.getNonExpiryQuantity());
                result.setReturnBatches(batchAnalysis.getNonExpiryBatches());
                result.setIsSeasonalProduct(false);
                result.setCalculationNote("选配目录中未找到该商品门店数据，保留数量=0");
                logger.debug("门店{}商品{}在选配目录中未找到，全部非近效期库存退仓", store.getOutId(), goodsNo);
                return result;
            }

            // 2. 判断是否季节商品
            boolean isSeasonalProduct = "是".equals(suggest.getSeasonalComponent());
            result.setIsSeasonalProduct(isSeasonalProduct);
            BigDecimal retainQuantity = BigDecimal.ZERO;
            String calculationNote = "";
            if (isSeasonalProduct) {
                // 季节商品：保留数量 = 去年同期后90天销售数量
                Double salesLastYearSamePeriodLast90dInt =  NumberUtil.parseNumber(suggest.getSalesLastYearSamePeriodLast90d()).doubleValue();
                BigDecimal salesLastYearSamePeriodLast90d = salesLastYearSamePeriodLast90dInt != null ?
                    BigDecimal.valueOf(salesLastYearSamePeriodLast90dInt) : BigDecimal.ZERO;
                retainQuantity = salesLastYearSamePeriodLast90d;
                result.setSalesLastYearSamePeriodLast90d(salesLastYearSamePeriodLast90d);
                calculationNote = String.format("季节商品，保留数量=%s（去年同期后90天销量）", retainQuantity);
                logger.debug("门店{}商品{}为季节商品，去年同期后90天销量：{}，保留数量：{}",
                        store.getOutId(), goodsNo, salesLastYearSamePeriodLast90d, retainQuantity);
            } else {
                // 非季节商品：需要判断近180天销量
                Double salesLast180dInt =  NumberUtil.parseNumber(suggest.getSalesLast180d()).doubleValue();
                Double salesLast30dInt =   NumberUtil.parseNumber(suggest.getSalesLast30d()).doubleValue();
                BigDecimal salesLast180d = salesLast180dInt != null ? BigDecimal.valueOf(salesLast180dInt) : BigDecimal.ZERO;
                BigDecimal salesLast30d = salesLast30dInt != null ? BigDecimal.valueOf(salesLast30dInt) : BigDecimal.ZERO;
                result.setSalesLast180d(salesLast180d);
                result.setSalesLast30d(salesLast30d);
                if (salesLast180d.compareTo(BigDecimal.ZERO) > 0) {
                    // 近180天销量>0，保留数量=max(近30天销量, 1)
                    retainQuantity = salesLast30d.max(BigDecimal.ONE);
                    calculationNote = String.format("非季节商品，近180天销量=%s>0，保留数量=max(%s,1)=%s",
                            salesLast180d, salesLast30d, retainQuantity);
                } else {
                    // 近180天销量<=0，保留数量=0
                    retainQuantity = BigDecimal.ZERO;
                    calculationNote = String.format("非季节商品，近180天销量=%s<=0，保留数量=0", salesLast180d);
                }

                logger.debug("门店{}商品{}为非季节商品，近180天销量：{}，近30天销量：{}，保留数量：{}",
                        store.getOutId(), goodsNo, salesLast180d, salesLast30d, retainQuantity);
            }

            // 3. 计算退仓数量
            BigDecimal nonExpiryQuantity = batchAnalysis.getNonExpiryQuantity();
            //非近效期需要退的数量
            BigDecimal nonExpiryReturnQuantity = nonExpiryQuantity.subtract(retainQuantity).max(BigDecimal.ZERO);

            result.setRetainQuantity(retainQuantity);//保留数量
            result.setReturnQuantity(nonExpiryReturnQuantity);//目前只包含非近效期需要退的数量
            result.setCalculationNote(calculationNote);
            // 4. 确定退仓批号（如果需要退仓）
            if (nonExpiryReturnQuantity.compareTo(BigDecimal.ZERO) > 0) {
                result.setReturnBatches(selectReturnBatches(batchAnalysis, nonExpiryReturnQuantity));
            } else {
                //不需要退则把这个 非效期批号数量清除下
                batchAnalysis.getNonExpiryBatchQuantitiesMap().clear();
                result.setReturnBatches(new ArrayList<>());
            }
            logger.debug("门店{}商品{}保留计算完成：非近效期库存{}，保留{}，退仓{}，说明：{}",
                    store.getOutId(), goodsNo, nonExpiryQuantity, retainQuantity, nonExpiryReturnQuantity, calculationNote);

            return result;

        } catch (Exception e) {
            logger.error("计算门店{}商品{}保留数量失败", store.getOutId(), goodsNo, e);

            // 异常情况下全部退仓
            result.setRetainQuantity(BigDecimal.ZERO);
            result.setReturnQuantity(batchAnalysis.getNonExpiryQuantity());
            result.setReturnBatches(batchAnalysis.getNonExpiryBatches());
            result.setIsSeasonalProduct(false);
            result.setCalculationNote("计算异常，全部退仓：" + e.getMessage());

            return result;
        }
    }
    /**
     * 选择需要退仓的批号
     * 优先选择较早过期的批号
     */
    private List<String> selectReturnBatches(BatchAnalysisResult batchAnalysis, BigDecimal returnQuantity) {
        List<String> returnBatches = new ArrayList<>();

        // 简单实现：按批号顺序选择  (实际应该按过期时间排序)
        Map<String, BigDecimal> batchQuantities = batchAnalysis.getNonExpiryBatchQuantitiesMap()
                .entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (k1, k2) -> k1,
                        LinkedHashMap::new  // 指定使用 LinkedHashMap 保持顺序
                ));
        BigDecimal remainingReturnQuantity = returnQuantity;
        Map<String, BigDecimal> nonExpiryBatchQuantitiesMap = batchAnalysis.getNonExpiryBatchQuantitiesMap();
        for (Map.Entry<String, BigDecimal> entry : batchQuantities.entrySet()) {
            String batchNo = entry.getKey();
            if (remainingReturnQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                logger.debug("退仓数量已满足，不退批号={} ", batchNo);
                nonExpiryBatchQuantitiesMap.remove(batchNo);
                break;
            }
            BigDecimal batchQuantity = entry.getValue();
            if (batchQuantity.compareTo(remainingReturnQuantity) <= 0) {
                // 整个批号都退仓
                returnBatches.add(batchNo);
                remainingReturnQuantity = remainingReturnQuantity.subtract(batchQuantity);
            } else {
                // 部分退仓（这种情况下也记录批号，具体数量=remainingReturnQuantity 最后一点应退数量）
                returnBatches.add(batchNo);
                nonExpiryBatchQuantitiesMap.put(batchNo,remainingReturnQuantity);
                remainingReturnQuantity = BigDecimal.ZERO;
            }
        }

        return returnBatches;
    }

    /**
     * 生成退仓单号(单号规则: 退仓接收仓库编码+门店编码+yyMMdd+001(3位自增顺序号))
     *
     * @param warehouseCode
     * @param storeCode
     * @return
     */
    private String genOrderNo(String warehouseCode, String storeCode) {
        String nowStr = DateUtils.conventDateStrByPattern(new Date(), "yyMMdd");
        RBucket<String> bucket = redissonClient.getBucket(ISCM_RETURN_WAREHOUSE_ORDER_CODE_CACHE + warehouseCode + storeCode + nowStr);
        if (null == bucket.get()) {
            bucket.set("001", 1L, TimeUnit.DAYS);
        } else {
            bucket.set(String.format("%03d", Integer.parseInt(bucket.get()) + 1), 1L, TimeUnit.DAYS);
        }
        return warehouseCode + storeCode + nowStr + "-" + bucket.get();
    }

    /**
     * 检查响应是否有效
     */
    private boolean isValidResponse(ResponseEntity<?> response) {
        return response != null
                && (HttpStatus.OK.equals(response.getStatusCode()) || HttpStatus.CREATED.equals(response.getStatusCode()))
                && response.getBody() != null;
    }
}
