package com.cowell.iscm.service.dto.unmanageGoodsReturn;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 批次分析结果DTO
 * 
 * <AUTHOR>
 */
public class BatchAnalysisResult implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 总库存数量
     */
    private BigDecimal totalQuantity;

    /**
     * 近效期库存数量
     */
    private BigDecimal nearExpiryQuantity;

    /**
     * 非近效期库存数量
     */
    private BigDecimal nonExpiryQuantity;

    /**
     * 近效期批号列表
     */
    private List<String> nearExpiryBatches;

    /**
     * 非近效期批号列表
     */
    private List<String> nonExpiryBatches;

    /**
     * 非近效期批号数量映射
     * Key: 批号, Value: 数量
     */
    private Map<String, BigDecimal> nonExpiryBatchQuantities;

    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public BigDecimal getNearExpiryQuantity() {
        return nearExpiryQuantity;
    }

    public void setNearExpiryQuantity(BigDecimal nearExpiryQuantity) {
        this.nearExpiryQuantity = nearExpiryQuantity;
    }

    public BigDecimal getNonExpiryQuantity() {
        return nonExpiryQuantity;
    }

    public void setNonExpiryQuantity(BigDecimal nonExpiryQuantity) {
        this.nonExpiryQuantity = nonExpiryQuantity;
    }

    public List<String> getNearExpiryBatches() {
        return nearExpiryBatches;
    }

    public void setNearExpiryBatches(List<String> nearExpiryBatches) {
        this.nearExpiryBatches = nearExpiryBatches;
    }

    public List<String> getNonExpiryBatches() {
        return nonExpiryBatches;
    }

    public void setNonExpiryBatches(List<String> nonExpiryBatches) {
        this.nonExpiryBatches = nonExpiryBatches;
    }

    public Map<String, BigDecimal> getNonExpiryBatchQuantities() {
        return nonExpiryBatchQuantities;
    }

    public void setNonExpiryBatchQuantities(Map<String, BigDecimal> nonExpiryBatchQuantities) {
        this.nonExpiryBatchQuantities = nonExpiryBatchQuantities;
    }

    @Override
    public String toString() {
        return "BatchAnalysisResult{" +
                "totalQuantity=" + totalQuantity +
                ", nearExpiryQuantity=" + nearExpiryQuantity +
                ", nonExpiryQuantity=" + nonExpiryQuantity +
                ", nearExpiryBatches=" + nearExpiryBatches +
                ", nonExpiryBatches=" + nonExpiryBatches +
                ", nonExpiryBatchQuantities=" + nonExpiryBatchQuantities +
                '}';
    }
}
