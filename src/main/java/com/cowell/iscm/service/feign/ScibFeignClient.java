package com.cowell.iscm.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.iscm.service.dto.SeasonalGoodsPushDTO;
import com.cowell.iscm.service.dto.applyParam.CommonListResponse;
import com.cowell.iscm.service.dto.applyParam.CommonRes;
import com.cowell.iscm.service.dto.storeAccessAuditor.AmisMap;
import com.cowell.iscm.service.dto.storeAccessAuditor.OptionDto;
import com.cowell.iscm.service.feign.dto.*;
import com.cowell.iscm.service.feign.dto.scib.RuleParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 从 scib 缓存中那组织机构信息 与用户权限无关  可以代替 permission 部分与用户无关 接口
 * <AUTHOR>
 * @date  2023年04月27日16:46:35
 *
 */
@FeignClient(name = "scib")
public interface ScibFeignClient {


    @ApiOperation(value = "根据sapCodeList和orgType(500/800)获取机构信息缓存", notes = "根据sapCodeList和orgType(500/800)获取机构信息缓存")
    @PostMapping("/api/internal/org/cache/getOrgBaseCacheBySapCode")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgBaseCacheBySapCode(@RequestParam("sapCodeList") List<String> sapCodeList, @RequestParam("orgType") int orgType);

    @ApiOperation(value = "根据outIdList和orgType(500/800)获取机构信息缓存", notes = "根据outIdList和orgType(500/800)获取机构信息缓存")
    @PostMapping("/api/internal/org/cache/getOrgBaseCacheByOutId")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgBaseCacheByOutId(@RequestParam("outIdList") List<Long> outIdList, @RequestParam("orgType") int orgType);

    @ApiOperation(value = "根据orgIdList和orgType(300/500/800)获取机构信息缓存", notes = "根据orgIdList和orgType(300/500/800)获取机构信息缓存")
    @PostMapping("/api/internal/org/cache/getOrgBaseCacheByOrgId")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgBaseCacheByOrgId(@RequestParam("orgIdList") List<Long> orgIdList, @RequestParam("orgType") int orgType);


    @ApiOperation(value = "根据businessId获取全部门店信息缓存", notes = "根据businessId获取全部门店信息缓存")
    @GetMapping("/api/internal/org/cache/getStoreListByBusinessId")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getStoreListByBusinessId(@RequestParam("businessId") Long businessId);


    @ApiOperation(value = "根据platformOrgId获取全部门店信息缓存", notes = "根据platformOrgId获取全部门店信息缓存")
    @GetMapping("/api/internal/org/cache/getStoreListByPlatformOrgId")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getStoreListByPlatformOrgId(@RequestParam("platformOrgId") Long platformOrgId);


    @ApiOperation(value = "批量获取门店/连锁缓存列表", notes = "批量获取门店/连锁缓存列表(By (type&orgIdList) 或storeIdList/businessIdList))")
    @PostMapping("/api/internal/org/cache/getOrgBaseCacheListByOutIdList")
    @Timed
    public ResponseEntity<List<OrgInfoBaseCache>> getOrgBaseCacheListByOutIdList(@RequestBody CommonOrgCacheDTO cacheDTO);


    @ApiOperation(value = "推送季节品数据", notes = "推送季节品数据)")
    @PostMapping("/api/internal/seasonGoods/updateMinDisplayBySeasonGoods")
    @Timed
    public ResponseEntity<CommonRes> updateMinDisplayBySeasonGoods(@RequestBody SeasonalGoodsPushDTO seasonalGoodsPushDTO);

    @ApiOperation("通用下拉框")
    @PostMapping("/api/common/dropBox")
    public ResponseEntity<CommonListResponse<OptionDto>> dropBox(@RequestBody DropBoxParam param);

    @ApiOperation("根据机构和配置参数去获取门店id列表")
    @GetMapping("/api/internal/common/getStoreIdListByStoreSelectorAndConfig")
    public ResponseEntity<CommonRes<ScibCommonResultDTO>> getStoreIdListByStoreSelectorAndConfig(@RequestParam("orgId") Long orgId, @RequestParam("scopeCode") String scopeCode, @RequestParam("configType") String configType);

    @ApiOperation("根据机构和配置参数去获取门店id列表")
    @PostMapping("/api/internal/rule/getRuleList")
    public ResponseEntity<CommonRes<AmisMap>> getRuleList(@RequestBody RuleParam ruleParam);

    @ApiOperation("根据门店ID和经营状态获取门店商品经营目录")
    @GetMapping("/api/internal/storeGoodsContents")
    public ResponseEntity<CommonRes<ScibCommonResultDTO>> getStoreGoodsContents(@RequestParam("storeId") Long storeId,
                                                                                @RequestParam("manageStatus") Integer manageStatus,
                                                                                @RequestParam(value = "goodsNoList", required = false) List<String> goodsNoList);

}
