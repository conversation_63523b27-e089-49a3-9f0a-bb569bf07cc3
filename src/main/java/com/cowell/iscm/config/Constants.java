package com.cowell.iscm.config;

/**
 * iscm常量
 * <AUTHOR>
 */
public final class Constants {

    public static final String APP_NAME = "iscm";

    // Regex for acceptable logins
    public static final String LOGIN_REGEX = "^[_'.@A-Za-z0-9-]*$";

    public static final String SYSTEM_ACCOUNT = "system";
    public static final String ANONYMOUS_USER = "anonymoususer";
    public static final String DEFAULT_LANGUAGE = "zh-cn";
    public static final String SPLIT_UNDERLINE = "_";
    public static final String SPLIT_MIDDLELINE = "-";
    public static final String SPLIT_CHINESE_COMMA = "，";
    public static final String SPLIT_ENGLISH_COMMA = ",";
    public static final String SPLIT_SPACE = " ";

    public static final String PAGE_NO = "page";
    public static final String PAGE_SIZE = "pageSize";
    public static final String ZERO = "0";
    public static final Integer INT_ZERO = 0;
    public static final Integer INT_ONE = 1;
    public static final Integer INT_TWO = 2;
    public static final Integer INT_THIRTY = 30;
    public static final Integer INT_NINETY = 90;
    public static final String BUSINESS_IDS = "businessIds";
    public static final String GOODS_CODES = "goodsCodes";
    public static final String PURCHASE_APPLY_NO = "purchaseApplicationNo";
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    public static final String DOCUMENT_STATUS = "documentStatus";
    public static final String AUDIT_STATUS = "auditStatus";

    /**通用删除状态值*/
    public static final Byte DELETE_STATUS = (byte) -1;
    /**通用正常状态值*/
    public static final Byte NORMAL_STATUS = (byte) 0;
    /**通用正常状态值(已处理)*/
    public static final Byte DONE_STATUS = (byte) 1;

    /**通用默认创建人/修改人*/
    public static final Long DEFAULT_CREATE_BY = -1L;
    /**通用默认创建人名字/修改人名字*/
    public static final String DEFAULT_CREATE_NAME = "系统管理员";
    /**批量插入一次最多插入数量*/
    public static final Integer BATCH_INSERT_ONCE_MAX_VALUE = 1000;
    /**调用feign接口一次最大请求量*/
    public static final Integer FEIGN_SERVICE_ONCE_MAX_VALUE = 400;
    /**调用forest接口一次最大请求量*/
    public static final Integer FEIGN_FOREST_SERVICE_ONCE_MAX_VALUE = 100;
    /**调用searchapi接口一次最大请求量*/
    public static final Integer FEIGN_SEARCHAPI_SERVICE_ONCE_MAX_VALUE = 100;

    /**
     * 推送数据到mb一次最大量
     */
    public static final Integer PUSH_TO_MB_ONCE_MAX_SIZE = 100;

    /**
     * mysql操作一次最大量
     */
    public static final Integer MYSQL_CRUD_ONCE_MAX_SIZE = 1000;

    /**
     * 最大页容量
     */
    public static final Integer MAX_PAGE_SIZE = 200;

    public static final String NO = "否";
    public static final String YES = "是";
    public static final String LIKE = "%";

    public static final String HD_ACTION = "pushPosSuggestApporve";
    public static final String UPDATE_REGISTER_SUGGEST_STATUS_ZSORT = "ISCM-UPDATE-REGISTER-SUGGEST-STATUS-ZSORT";
    public static final String DEFAULT_BUSINESSID = "10000";

    public static final Integer DEFAULT_MAX_QUERY_COUNT = 10000;

    /**批量删除一次最多删除数量*/
    public static final Integer BATCH_DELETE_ONCE_MAX_VALUE = 5000;

    /**
     * 预警发邮件的Token
     */
    public static final String TOKEN_RISK_SEND_MAIL = "ec5a663aef684192b53d7aec0a3c2960";

    /**
     * 下载中心平台类型
     */
    public static final Integer PLATFORM_TYPE = 7;

    /**
     * 文件下载状态： 2-成功
     */
    public static final Integer FILE_DOWN_STATUS_SUCCESS = 2;

    /**
     * 文件下载状态： 3-失败
     */
    public static final Integer FILE_DOWN_STATUS_FAIL = 3;

    public static final String ISCM_GOOGS_CATEGORY_LIST = "ISCM_GOOGS_CATEGORY_LIST";

    public static final String  GJYL = "高济医疗";

    /**调用feign item_searchapi接口一次最大请求量*/
    public static final Integer FEIGN_ITEM_SEARCH_ONCE_MAX_VALUE = 30;

    public static final Byte HD_PUSH_DEL_DATA_STATUS = 0;

    public static final Byte HD_PUSH_INSERT_DATA_STATUS = 1;

    public static Integer QUERY_SEARCH_PAGESIZE = 50;

    public static Integer INSERT_MAX_SIZE = 200;
    public static Integer BATCH_UPDATE_MAX_SIZE = 500;

    public static final String MIDDLE_LINE = "-";

    // 智慧供应链管理员角色编码
    public static final String ROLE_GLY = "ZHGYLGLY";

    // 业务标识码
    public final static String BIZ_CODE = "CLOUD_POS";
    public final static String ZDT_CLIENT_ID = "ZDT";
    public final static String ISCM_RETURN_REASON_FLAG = "iscm_hd_returnReasons";
    public final static Integer ISCM_RETURN_REASON_TYPE = 4;
    public final static String ISCM_RETURN_REASON_DEFAULT = "BC";
    public final static String ISCM_RETURN_REASON_NAME_DEFAULT = "推荐退仓";
    public final static String ISCM_DEFECTIVE_RETURN_REASON_DEFAULT = "12";
    public final static String ISCM_DEFECTIVE_RETURN_REASON_NAME_DEFAULT = "效期品种退货";

    public final static String ISCM_DEFECTIVE_RETURN_REASON_NON_EXPIRY = "06";
    /**
     * 店长角色编码
     */
    public static final String DZ_ROLE_CODE = "DZ";

    private Constants() {
    }
}
